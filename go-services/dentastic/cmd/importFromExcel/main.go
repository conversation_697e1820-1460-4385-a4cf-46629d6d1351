package main

import (
	"database/sql"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/xuri/excelize/v2"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
)

const dateFormat = "2006-01-02"

func main() {
	fileName := flag.String("file", "Book1.xlsx", "Name of the file to read from")
	filePassword := flag.String("password", "", "Password of the file to read from")
	sheetName := flag.String("sheet", "Sheet1", "Name of the sheet to pull the data from")
	clinicId := flag.String("clinic", "", "ID of clinic to add patients to")
	patientFileNumberField := flag.String("file-number-field", "A", "Name of the field to pull the file number from")
	patientNameField := flag.String("file-name-field", "B", "Name of the field to pull the file name from")
	patientAgeField := flag.String("file-age-field", "C", "Name of the field to pull the file age from")
	patientVisitDateField := flag.String("file-visit-date-field", "D", "Name of the field to pull the file visit date from")
	patientPhoneNumberField := flag.String("file-phone-number-field", "E", "Name of the field to pull the file phone number from")
	patientAddressField := flag.String("file-address-field", "F", "Name of the field to pull the file address from")
	hasHeader := flag.Bool("has-header", true, "Defines if the file has a header row")
	flag.Parse()

	f, err := excelize.OpenFile(*fileName, excelize.Options{
		Password: *filePassword,
	})
	if err != nil {
		panic(err)
	}
	defer func() {
		err = f.Close()
		if err != nil {
			panic(err)
		}
	}()
	list := f.GetSheetList()
	fmt.Println("Sheets:", strings.Join(list, ", "))
	rows, err := f.GetRows(*sheetName)
	if err != nil {
		panic(err)
	}
	startFrom := firstNonEmptyRow(rows)
	if startFrom == -1 {
		panic("No data found")
	}
	if *hasHeader {
		startFrom++
	}
	// check if file naab-import.db exists
	_, err = os.Open("./naab-import.db")
	if err != nil {
		if !os.IsNotExist(err) {
			panic(err)
		}
		os.Remove("./naab-import.txt")
	}
	dbFile, err := os.Create("./naab-import.txt")
	if err != nil {
		panic(err)
	}
	defer dbFile.Close()

	db, err := gorm.Open(sqlite.Open("naab-import.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error),
	})
	if err != nil {
		panic(err)
	}
	FileNumberIndex, err := excelize.ColumnNameToNumber(*patientFileNumberField)
	if err != nil {
		panic(err)
	}
	FileNumberIndex--
	NameIndex, err := excelize.ColumnNameToNumber(*patientNameField)
	if err != nil {
		panic(err)
	}
	NameIndex--
	AgeIndex, err := excelize.ColumnNameToNumber(*patientAgeField)
	if err != nil {
		panic(err)
	}
	AgeIndex--
	VisitDateIndex, err := excelize.ColumnNameToNumber(*patientVisitDateField)
	if err != nil {
		panic(err)
	}
	VisitDateIndex--
	PhoneNumberIndex, err := excelize.ColumnNameToNumber(*patientPhoneNumberField)
	if err != nil {
		panic(err)
	}
	PhoneNumberIndex--
	AddressIndex, err := excelize.ColumnNameToNumber(*patientAddressField)
	if err != nil {
		panic(err)
	}
	AddressIndex--
	fmt.Println("FileNumberIndex:", FileNumberIndex)
	fmt.Println("NameIndex:", NameIndex)
	fmt.Println("AgeIndex:", AgeIndex)
	fmt.Println("VisitDateIndex:", VisitDateIndex)
	fmt.Println("PhoneNumberIndex:", PhoneNumberIndex)
	fmt.Println("AddressIndex:", AddressIndex)
	patients := []lib.DentalClinicPatient{}
	db.AutoMigrate(&lib.DentalClinicPatient{})
	for _, row := range rows[startFrom:] {
		if len(row) < 2 {
			continue
		}
		for i, colCell := range row {
			fmt.Print(i, ":", colCell, "\t")
		}
		fmt.Println()
		p := lib.DentalClinicPatient{}
		p.ClinicID = *clinicId
		p.FileNumber = row[FileNumberIndex]
		p.Name = row[NameIndex]
		p.Birthdate = sql.NullString{Valid: false}
		if len(extractNumber(row[AgeIndex])) > 0 {
			diff, err := strconv.Atoi(extractNumber(row[AgeIndex]))
			if err != nil {
				fmt.Println("Failed to parse age: ", row[AgeIndex])
			} else {
				p.Birthdate = sql.NullString{Valid: true, String: time.Date(time.Now().Year()-diff, 1, 1, 12, 0, 0, 0, time.UTC).Format(dateFormat)}
			}
		}

		d, err := time.Parse(dateFormat, row[VisitDateIndex])
		if err != nil {
			fmt.Println("Failed to parse visit date: ", row[VisitDateIndex])
			p.CreatedAt = models.CreatedAt{
				CreatedAt: time.Now(),
			}
		} else {
			p.CreatedAt = models.CreatedAt{
				CreatedAt: d,
			}
		}
		p.PhoneNumber = row[PhoneNumberIndex]
		p.Address = row[AddressIndex]
		patients = append(patients, p)
		// for _, colCell := range row {
		// 	fmt.Print(colCell, "\t")
		// }
		// fmt.Println()
	}
	for _, p := range patients {
		j, err := json.MarshalIndent(lib.JsonPatient{DentalClinicPatient: patients[0]}, "", "\t")
		if err != nil {
			panic(err)
		}
		fmt.Println(string(j))
		db.Create(&p)
		line := db.ToSQL(func(tx *gorm.DB) *gorm.DB {
			return tx.Create(&p).Clauses(clause.Returning{})
		})
		_, err = dbFile.WriteString(line + "\n")
		if err != nil {
			panic(err)
		}
		// fmt.Println(line)
	}
	/* j, err := json.MarshalIndent(lib.JsonPatient{DentalClinicPatient: patients[0]}, "", "\t")
	if err != nil {
		panic(err)
	}
	fmt.Println(string(j)) */
}

/* func checkNumric(value string) bool {
	for _, c := range value {
		if c < '0' || c > '9' {
			return false
		}
	}
	return true
} */

func extractNumber(value string) string {
	var result string
	for _, c := range value {
		if c >= '0' && c <= '9' {
			result += string(c)
		} else {
			break
		}
	}
	return result
}

func firstNonEmptyRow(rows [][]string) int {
	for i, row := range rows {
		for _, colCell := range row {
			if colCell != "" {
				return i
			}
		}
	}
	return -1
}
