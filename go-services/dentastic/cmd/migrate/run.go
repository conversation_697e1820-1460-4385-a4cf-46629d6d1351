package main

import (
	"flag"
	"log"

	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
)

func main() {
	migrateMerchant := flag.Bool("migrate-merchant", false, "Ask the database to migrate the merchant table")
	flag.Parse()

	db, err := lib.InitializeDB()
	if err != nil {
		log.Fatalln(err)
	}
	if *migrateMerchant {
		log.Println("Migrating merchant")
		err = models.MigrateMerchant(db)
		if err != nil {
			panic(err)
		}
	}

	log.Println("Migrating NAAB tables")
	err = db.AutoMigrate(
		// models.Merchant{},
		&lib.DentalClinicSubscriptionPlan{},
		&lib.DentalClinic{},
		&lib.DentalClinicUser{},
		&lib.DentalClinicBranch{},
		&lib.DentalClinicPatient{},
		&lib.DentalClinicPatientFile{},
		&lib.DentalClinicAppointment{},
		&lib.DentalClinicExpense{},
		&lib.DentalClinicInventoryItem{},
		&lib.DentalClinicInventoryItemTransaction{},
		&lib.DentalClinicSpecialityTemplate{},
		&lib.DentalClinicProcedureTemplate{},
		&lib.DentalClinicVisit{},
		&lib.DentalClinicVisitProcedure{},
		&lib.DentalClinicPatientPayment{},
		&lib.DentalClinicLab{},
		&lib.DentalClinicLabRequest{},
	)
	if err != nil {
		panic(err)
	}
	log.Println("Migration completed.")
}
