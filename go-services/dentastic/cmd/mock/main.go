package main

import (
	"fmt"

	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	db, err := gorm.Open(sqlite.Open("mock.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error),
	})
	if err != nil {
		panic(err)
	}

	password, err := bcrypt.GenerateFromPassword([]byte("password"), 10)
	if err != nil {
		panic(err)
	}
	merchants := []models.Merchant{}
	for i := 0; i < 10; i++ {
		merchant := models.Merchant{
			DisplayName:  "Merchant " + string(i),
			Email:        "merchant" + string(i) + "@example.com",
			SystemName:   "merchant" + string(i),
			PhoneNumber:  "0100000000" + string(i),
			Type:         models.MerchantTypeDentalClinic,
			PlatformRate: 0.05,
			Password:     string(password),
		}
		db.Create(&merchant)
		merchants = append(merchants, merchant)
	}
	clinics := make([]lib.DentalClinic, len(merchants))
	for i, merchant := range merchants {
		clinic := lib.DentalClinic{
			Name:        merchant.DisplayName,
			DisplayName: merchant.DisplayName,
			Currency:    "EGP",
			Country:     "EG",
			Credit:      0,
		}
		clinic.MerchantID = merchant.ID
		db.Create(&clinic)
		clinics[i] = clinic
	}
	for i, clinic := range clinics {
		user := lib.DentalClinicUser{
			ClinicID:  clinic.ID,
			Username:  "user" + fmt.Sprintf("%v", i),
			Name:      "User " + fmt.Sprintf("%v", i),
			Password:  string(password),
			Role:      lib.DentalClinicRoleMaster,
			IsDentist: true,
		}
		db.Create(&user)
	}
}
