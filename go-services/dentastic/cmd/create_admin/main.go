package main

import (
	"flag"
	"fmt"
	"log"
	"time"

	"gitlab.com/payrows/dentastic/lib"
	"gitlab.com/payrows/dentastic/lib/admin"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// Define command line flags
	email := flag.String("email", "", "Email for the admin user")
	password := flag.String("password", "", "Password for the admin user")
	firstName := flag.String("first-name", "", "First name for the admin user")
	lastName := flag.String("last-name", "", "Last name for the admin user")
	flag.Parse()

	// Validate required parameters
	if *email == "" {
		log.Fatal("Email is required. Use -email flag.")
	}
	if *password == "" {
		log.Fatal("Password is required. Use -password flag.")
	}
	if len(*password) < 8 {
		log.Fatal("Password must be at least 8 characters long.")
	}

	// Initialize database connection
	db, err := lib.InitializeDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// Ensure admin tables are migrated
	err = db.AutoMigrate(&admin.Admin{}, &admin.Permission{}, &admin.AdminPermission{}, &admin.AdminActionLog{})
	if err != nil {
		log.Fatalf("Failed to migrate admin tables: %v", err)
	}

	// Initialize permissions
	err = admin.InitPermissions(db)
	if err != nil {
		log.Fatalf("Failed to initialize permissions: %v", err)
	}

	// Check if admin with this email already exists
	var existingAdmin admin.Admin
	if err := db.Where("email = ?", *email).First(&existingAdmin).Error; err == nil {
		log.Fatalf("Admin with email %s already exists", *email)
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatalf("Failed to hash password: %v", err)
	}

	// Create new admin
	newAdmin := admin.Admin{
		Email:     *email,
		Password:  string(hashedPassword),
		FirstName: *firstName,
		LastName:  *lastName,
		IsActive:  true,
	}

	// Start a transaction
	tx := db.Begin()
	if tx.Error != nil {
		log.Fatalf("Failed to start transaction: %v", tx.Error)
	}

	// Create the admin
	if err := tx.Create(&newAdmin).Error; err != nil {
		tx.Rollback()
		log.Fatalf("Failed to create admin: %v", err)
	}

	fmt.Printf("Created admin user with ID: %s\n", newAdmin.ID)

	// Get all permissions
	var permissions []admin.Permission
	if err := tx.Find(&permissions).Error; err != nil {
		tx.Rollback()
		log.Fatalf("Failed to fetch permissions: %v", err)
	}

	// Assign all permissions to the admin
	for _, permission := range permissions {
		adminPerm := admin.AdminPermission{
			AdminID:      newAdmin.ID,
			PermissionID: permission.ID,
			CreatedAt:    time.Now(),
			CreatedBy:    "create_admin_script",
		}
		if err := tx.Create(&adminPerm).Error; err != nil {
			tx.Rollback()
			log.Fatalf("Failed to assign permission %s: %v", permission.Name, err)
		}
		fmt.Printf("Assigned permission: %s\n", permission.Name)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		log.Fatalf("Failed to commit transaction: %v", err)
	}

	fmt.Printf("\nSuccessfully created admin user with email: %s\n", *email)
	fmt.Println("All permissions have been assigned to this admin.")
}
