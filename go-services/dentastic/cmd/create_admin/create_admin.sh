#!/bin/bash

# Navigate to the project root directory
cd "$(dirname "$0")/../.."

# Build the create_admin tool
go build -o create_admin cmd/create_admin/main.go

# Check if build was successful
if [ $? -ne 0 ]; then
    echo "Failed to build create_admin tool"
    exit 1
fi

# Run the create_admin tool with provided arguments
./create_admin "$@"

# Remove the binary after execution
rm create_admin

echo "Done!"
