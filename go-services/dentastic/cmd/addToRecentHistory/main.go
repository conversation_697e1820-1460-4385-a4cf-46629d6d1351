package main

import (
	"database/sql"
	"encoding/json"
	"log"
	"strconv"
	"time"

	"github.com/lib/pq"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
	"gorm.io/gorm"
)

func main() {
	_, server := lib.Initialize(lib.IntializationConfig{})
	db := server.Db
	clinicId := "sYFertgVrI9p7_5u"
	clinic := lib.DentalClinic{}
	if err := db.Where("id = ?", clinicId).Take(&clinic).Error; err != nil {
		panic(err)
	}
	branch := lib.DentalClinicBranch{}
	if err := db.Where("clinic_id = ?", clinic.ID).Take(&branch).Error; err != nil {
		panic(err)
	}
	patients := []lib.DentalClinicPatient{}
	if err := db.Where("clinic_id = ?", clinic.ID).Limit(10).Find(&patients).Error; err != nil {
		panic(err)
	}
	dentist := lib.DentalClinicUser{}
	if err := db.Where("clinic_id = ?", clinic.ID).Take(&dentist).Error; err != nil {
		panic(err)
	}
	appointments := make([]lib.DentalClinicAppointment, len(patients))
	visits := make([]lib.DentalClinicVisit, len(patients))
	db.Transaction(func(trx *gorm.DB) error {
		for index := range patients {
			date := time.Now().Add(-1 * time.Hour * 24 * time.Duration(index))
			appointment := lib.DentalClinicAppointment{
				BranchID:  branch.ID,
				ClinicID:  clinic.ID,
				PatientID: patients[index].ID,
				StartTime: date,
				EndTime:   date.Add(time.Minute * 45),
				Room:      1,
				DentistID: sql.NullString{
					Valid:  true,
					String: dentist.ID,
				},
			}
			appointments[index] = appointment
		}
		if err := trx.Create(&appointments).Error; err != nil {
			panic(err)
		}
		for index := range appointments {
			visits[index] = lib.DentalClinicVisit{
				ClinicID:      clinic.ID,
				BranchID:      branch.ID,
				AppointmentID: appointments[index].ID,
				PatientID:     appointments[index].PatientID,
				Diagnosis:     "Diagnosis " + strconv.Itoa(index),
				Treatments:    pq.StringArray{},
				Comments:      "",
				NextVisit:     "",
				DBModelWithCreatedAt: models.DBModelWithCreatedAt{
					CreatedAt: models.CreatedAt{
						CreatedAt: appointments[index].StartTime,
					},
				},
			}
		}
		data, _ := json.Marshal(lib.JsonVisit{DentalClinicVisit: visits[0]})
		log.Println(string(data))
		if err := trx.Create(&visits).Error; err != nil {
			panic(err)
		}
		return nil
	})
}
