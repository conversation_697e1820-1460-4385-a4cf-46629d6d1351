# kind: Service
# apiVersion: v1
# metadata:
#   name:  dentastic
#   annotations:
#     service.beta.kubernetes.io/do-loadbalancer-name: "dentastic-fra1"
#     service.beta.kubernetes.io/do-loadbalancer-protocol: "http"
#     service.beta.kubernetes.io/do-loadbalancer-algorithm: "round_robin"
#     service.beta.kubernetes.io/do-loadbalancer-http-ports: "80"
#     service.beta.kubernetes.io/do-loadbalancer-tls-ports: "443"
#     service.beta.kubernetes.io/do-loadbalancer-redirect-http-to-https: "true"
#     service.beta.kubernetes.io/do-loadbalancer-certificate-id: "8d99f270-c349-4b2e-9459-3a62196965c4"
#     kubernetes.digitalocean.com/load-balancer-id: "346d5b95-d674-4798-9dd9-fb20eae58b27"
#     service.beta.kubernetes.io/do-loadbalancer-size-unit: "1"
#     # service.beta.kubernetes.io/do-loadbalancer-disable-lets-encrypt-dns-records: "false"
# spec:
#   selector:
#     app:  dentastic
#   type:  LoadBalancer
#   ports:
#   - name:  https
#     port:  443
#     targetPort:  80
#     protocol: TCP
#   - name:  http
#     port:  80
#     targetPort:  80
#     protocol: TCP
# ---
kind: Service
apiVersion: v1
metadata:
  name:  dentastic
spec:
  selector:
    app:  dentastic
  type:  ClusterIP
  ports:
  - name:  https
    port:  443
    targetPort:  80
    protocol: TCP
  - name:  http
    port:  80
    targetPort:  80
    protocol: TCP
---