apiVersion: v1
kind: Pod
metadata:
  name: "naab-migrate"
  namespace: default
  labels:
    app: "naab-migrate"
spec:
  imagePullSecrets:
    - name: regcred
  containers:
  - name: naab-migrate
    image: "registry.gitlab.com/omarwaleed/payrows/dentastic:latest"
    imagePullPolicy: Always
    command: ["/app/server"]
    # args: ["run", "cmd/migrate/run.go", "--migrate-merchant"]
    args: ["--migrate", "--exit-after-migrate"]
    env:
    - name: DB_URL
      valueFrom:
        secretKeyRef:
          name: config
          key: postgres-url
  restartPolicy: Never
---