package tests

import (
	"encoding/json"
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

func TestAuthenticationFlow(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Complete Authentication Flow", func(t *testing.T) {
		// Get subscription plan for clinic creation
		plan := new(lib.DentalClinicSubscriptionPlan)
		if err := server.Db.First(plan).Error; err != nil {
			t.Fatalf("Failed to get subscription plan: %v", err)
		}

		// Test clinic registration
		clinicReq := ClinicCreateRequest{
			DisplayName:      "Test Dental Clinic",
			Username:         "testadmin",
			Name:             "Dr. Test Admin",
			Email:            "<EMAIL>",
			Password:         "password123",
			PhoneNumber:      "01234567890",
			SystemName:       "testclinic",
			SubscriptionPlan: plan.ID,
		}

		resp := client.Request("POST", "/clinics", clinicReq, nil)
		if resp.Code != 201 {
			t.Fatalf("Clinic creation failed with status %d: %s", resp.Code, resp.Body.String())
		}

		var createResult map[string]interface{}
		if err := json.Unmarshal(resp.Body.Bytes(), &createResult); err != nil {
			t.Fatalf("Failed to parse clinic creation response: %v", err)
		}

		// Verify token is returned
		token, ok := createResult["token"].(string)
		if !ok || token == "" {
			t.Fatal("Token not returned in clinic creation response")
		}

		// Test login with correct credentials
		loginReq := LoginRequest{
			ClinicName: "testclinic",
			Username:   "testadmin",
			Password:   "password123",
		}

		resp = client.Request("POST", "/login", loginReq, nil)
		if resp.Code != 200 {
			t.Fatalf("Login failed with status %d: %s", resp.Code, resp.Body.String())
		}

		var loginResult map[string]interface{}
		if err := json.Unmarshal(resp.Body.Bytes(), &loginResult); err != nil {
			t.Fatalf("Failed to parse login response: %v", err)
		}

		loginToken, ok := loginResult["token"].(string)
		if !ok || loginToken == "" {
			t.Fatal("Token not returned in login response")
		}

		// Test authenticated request
		resp = client.RequestWithAuth("GET", "/users/me", nil, loginToken)
		if resp.Code != 200 {
			t.Fatalf("Authenticated request failed with status %d: %s", resp.Code, resp.Body.String())
		}
	})
}

func TestLoginValidation(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Create test clinic and user
	clinic, user, _ := testData.CreateTestClinic("logintest")

	tests := []struct {
		name           string
		request        LoginRequest
		expectedStatus int
		description    string
	}{
		{
			name: "Valid Login",
			request: LoginRequest{
				ClinicName: clinic.Name,
				Username:   user.Username,
				Password:   "password",
			},
			expectedStatus: 200,
			description:    "Should succeed with correct credentials",
		},
		{
			name: "Wrong Password",
			request: LoginRequest{
				ClinicName: clinic.Name,
				Username:   user.Username,
				Password:   "wrongpassword",
			},
			expectedStatus: 401,
			description:    "Should fail with wrong password",
		},
		{
			name: "Wrong Username",
			request: LoginRequest{
				ClinicName: clinic.Name,
				Username:   "wronguser",
				Password:   "password",
			},
			expectedStatus: 401,
			description:    "Should fail with wrong username",
		},
		{
			name: "Wrong Clinic",
			request: LoginRequest{
				ClinicName: "wrongclinic",
				Username:   user.Username,
				Password:   "password",
			},
			expectedStatus: 404,
			description:    "Should fail with wrong clinic name",
		},
		{
			name: "Missing Clinic Name",
			request: LoginRequest{
				ClinicName: "",
				Username:   user.Username,
				Password:   "password",
			},
			expectedStatus: 400,
			description:    "Should fail with missing clinic name",
		},
		{
			name: "Missing Username",
			request: LoginRequest{
				ClinicName: clinic.Name,
				Username:   "",
				Password:   "password",
			},
			expectedStatus: 400,
			description:    "Should fail with missing username",
		},
		{
			name: "Missing Password",
			request: LoginRequest{
				ClinicName: clinic.Name,
				Username:   user.Username,
				Password:   "",
			},
			expectedStatus: 400,
			description:    "Should fail with missing password",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := client.Request("POST", "/login", tt.request, nil)
			if resp.Code != tt.expectedStatus {
				t.Errorf("%s: Expected status %d, got %d. Response: %s",
					tt.description, tt.expectedStatus, resp.Code, resp.Body.String())
			}
		})
	}
}

func TestClinicRegistrationValidation(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Get subscription plan
	plan := new(lib.DentalClinicSubscriptionPlan)
	if err := server.Db.First(plan).Error; err != nil {
		t.Fatalf("Failed to get subscription plan: %v", err)
	}

	tests := []struct {
		name           string
		request        ClinicCreateRequest
		expectedStatus int
		description    string
	}{
		{
			name: "Valid Registration",
			request: ClinicCreateRequest{
				DisplayName:      "Valid Clinic",
				Username:         "validuser",
				Name:             "Dr. Valid User",
				Email:            "<EMAIL>",
				Password:         "password123",
				PhoneNumber:      "01234567890",
				SystemName:       "validclinic",
				SubscriptionPlan: plan.ID,
			},
			expectedStatus: 201,
			description:    "Should succeed with valid data",
		},
		{
			name: "Short Password",
			request: ClinicCreateRequest{
				DisplayName:      "Test Clinic",
				Username:         "testuser",
				Name:             "Dr. Test",
				Email:            "<EMAIL>",
				Password:         "short",
				PhoneNumber:      "01234567890",
				SystemName:       "testclinic2",
				SubscriptionPlan: plan.ID,
			},
			expectedStatus: 400,
			description:    "Should fail with password too short",
		},
		{
			name: "Missing Email",
			request: ClinicCreateRequest{
				DisplayName:      "Test Clinic",
				Username:         "testuser",
				Name:             "Dr. Test",
				Email:            "",
				Password:         "password123",
				PhoneNumber:      "01234567890",
				SystemName:       "testclinic3",
				SubscriptionPlan: plan.ID,
			},
			expectedStatus: 400,
			description:    "Should fail with missing email",
		},
		{
			name: "Invalid Username",
			request: ClinicCreateRequest{
				DisplayName:      "Test Clinic",
				Username:         "a", // Too short
				Name:             "Dr. Test",
				Email:            "<EMAIL>",
				Password:         "password123",
				PhoneNumber:      "01234567890",
				SystemName:       "testclinic4",
				SubscriptionPlan: plan.ID,
			},
			expectedStatus: 400,
			description:    "Should fail with invalid username",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp := client.Request("POST", "/clinics", tt.request, nil)
			if resp.Code != tt.expectedStatus {
				t.Errorf("%s: Expected status %d, got %d. Response: %s",
					tt.description, tt.expectedStatus, resp.Code, resp.Body.String())
			}
		})
	}
}

func TestDuplicateRegistration(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Get subscription plan
	plan := new(lib.DentalClinicSubscriptionPlan)
	if err := server.Db.First(plan).Error; err != nil {
		t.Fatalf("Failed to get subscription plan: %v", err)
	}

	// Create first clinic
	clinicReq := ClinicCreateRequest{
		DisplayName:      "First Clinic",
		Username:         "firstuser",
		Name:             "Dr. First",
		Email:            "<EMAIL>",
		Password:         "password123",
		PhoneNumber:      "01234567890",
		SystemName:       "firstclinic",
		SubscriptionPlan: plan.ID,
	}

	resp := client.Request("POST", "/clinics", clinicReq, nil)
	if resp.Code != 201 {
		t.Fatalf("First clinic creation failed: %s", resp.Body.String())
	}

	// Try to create clinic with same email
	duplicateEmailReq := clinicReq
	duplicateEmailReq.SystemName = "secondclinic"
	duplicateEmailReq.DisplayName = "Second Clinic"

	resp = client.Request("POST", "/clinics", duplicateEmailReq, nil)
	if resp.Code != 409 {
		t.Errorf("Expected 409 for duplicate email, got %d: %s", resp.Code, resp.Body.String())
	}

	// Try to create clinic with same phone number
	duplicatePhoneReq := clinicReq
	duplicatePhoneReq.Email = "<EMAIL>"
	duplicatePhoneReq.SystemName = "thirdclinic"
	duplicatePhoneReq.DisplayName = "Third Clinic"

	resp = client.Request("POST", "/clinics", duplicatePhoneReq, nil)
	if resp.Code != 409 {
		t.Errorf("Expected 409 for duplicate phone, got %d: %s", resp.Code, resp.Body.String())
	}
}

func TestTokenAuthentication(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Create test clinic and user
	clinic, user, _ := testData.CreateTestClinic("tokentest")

	// Get valid token
	token := client.GetAuthToken(clinic.Name, user.Username, "password")

	t.Run("Valid Token", func(t *testing.T) {
		resp := client.RequestWithAuth("GET", "/users/me", nil, token)
		if resp.Code != 200 {
			t.Errorf("Valid token should work, got status %d: %s", resp.Code, resp.Body.String())
		}
	})

	t.Run("Missing Token", func(t *testing.T) {
		resp := client.Request("GET", "/users/me", nil, nil)
		if resp.Code != 401 {
			t.Errorf("Missing token should return 401, got %d", resp.Code)
		}
	})

	t.Run("Invalid Token", func(t *testing.T) {
		resp := client.RequestWithAuth("GET", "/users/me", nil, "invalid.token.here")
		if resp.Code != 401 {
			t.Errorf("Invalid token should return 401, got %d", resp.Code)
		}
	})

	t.Run("Empty Token", func(t *testing.T) {
		resp := client.RequestWithAuth("GET", "/users/me", nil, "")
		if resp.Code != 401 {
			t.Errorf("Empty token should return 401, got %d", resp.Code)
		}
	})
}
