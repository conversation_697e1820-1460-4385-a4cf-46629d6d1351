# Dentastic Integration Tests

This directory contains comprehensive integration tests for the Dentastic dental clinic management system. The tests are designed to work with SQLite for fast, isolated testing while maintaining compatibility with the production PostgreSQL database. The tests are designed to ensure quality before production deployments and when adding new features.

## Test Structure

### Test Files

- **`main_test.go`** - Test setup, teardown, and configuration
- **`test_utils.go`** - Test utilities, helpers, and data factories
- **`auth_test.go`** - Authentication and authorization tests
- **`merchant_test.go`** - Merchant/clinic management tests
- **`database_test.go`** - Database integration and data persistence tests
- **`error_handling_test.go`** - Error scenarios and edge cases
- **`login_test.go`** - Legacy login tests (enhanced)

### Test Categories

#### 1. Authentication Tests (`auth_test.go`)
- Complete authentication flow (registration → login → authenticated requests)
- Login validation with various invalid inputs
- Clinic registration validation
- Duplicate registration prevention
- JWT token authentication and validation

#### 2. Merchant/Clinic Tests (`merchant_test.go`)
- Clinic information management
- User profile operations
- User management (create, list, update)
- Role-based access control (Master, Admin, Basic users)
- Subscription validation and enforcement

#### 3. Database Integration Tests (`database_test.go`)
- Database connectivity and basic operations
- Model creation and relationship loading
- Patient management (CRUD operations)
- Appointment management with relationships
- Transaction rollback testing
- Foreign key constraint validation
- Null value handling

#### 4. Error Handling Tests (`error_handling_test.go`)
- Invalid JSON request handling
- Missing required fields validation
- Resource not found scenarios
- Unauthorized access prevention
- Large request handling
- Concurrent access testing
- SQL injection prevention
- Input validation
- Response format consistency

## Test Configuration

### Database Setup
- Uses SQLite for fast, isolated testing
- Each test run creates a unique temporary database file
- Automatic cleanup after test completion
- Full schema migration during setup

### Environment Variables
The tests set up the following environment variables:
- `DB_URL` - SQLite database path
- `PORT` - Test server port (3010)
- `JWT_SECRET` - Test JWT secret
- `S3_KEY`, `S3_SECRET` - Test S3 credentials
- `STRIPE_KEY` - Test Stripe key

### Test Data Management
- **Test Data Factories**: Utilities to create consistent test data
- **Automatic Cleanup**: Each test cleans up its data
- **Isolation**: Tests are independent and don't affect each other
- **Realistic Data**: Test data mimics real-world scenarios

## Running Tests

### Prerequisites
1. Go 1.21 or later
2. All project dependencies installed (`go mod download`)

### Run All Tests
```bash
cd go-services/dentastic
go test ./tests/... -v
```

### Run Specific Test Files
```bash
# Authentication tests only
go test ./tests/auth_test.go ./tests/main_test.go ./tests/test_utils.go -v

# Database tests only
go test ./tests/database_test.go ./tests/main_test.go ./tests/test_utils.go -v

# Error handling tests only
go test ./tests/error_handling_test.go ./tests/main_test.go ./tests/test_utils.go -v
```

### Run Specific Test Functions
```bash
# Run only authentication flow test
go test ./tests/... -run TestAuthenticationFlow -v

# Run only role-based access tests
go test ./tests/... -run TestRoleBasedAccess -v
```

### Test Coverage
```bash
# Generate coverage report
go test ./tests/... -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

## Test Utilities

### TestClient
Wrapper around the Fiber app for easier HTTP testing:
- `Request(method, path, body, headers)` - Make HTTP requests
- `RequestWithAuth(method, path, body, token)` - Make authenticated requests
- `GetAuthToken(clinicName, username, password)` - Get JWT token for authentication
- `AssertStatusCode(resp, expected)` - Assert response status code
- `AssertJSONResponse(resp, expectedFields)` - Assert JSON response format and fields

### TestData
Factory for creating test data:
- `CreateTestClinic(name)` - Create clinic with merchant and master user
- `CreateTestUser(clinicID, name, username, role)` - Create additional users
- `CreateTestPatient(clinicID, name, phoneNumber)` - Create test patients
- `CleanupTestData()` - Remove all test data

## CI/CD Integration

### GitHub Actions / GitLab CI
```yaml
test:
  script:
    - cd go-services/dentastic
    - go test ./tests/... -v -race -coverprofile=coverage.out
    - go tool cover -func=coverage.out
```

### Docker Testing
```dockerfile
FROM golang:1.21-alpine
WORKDIR /app
COPY . .
RUN go mod download
RUN go test ./tests/... -v
```

## Best Practices

### Test Organization
- Each test file focuses on a specific domain (auth, merchant, database, etc.)
- Tests are grouped into logical test functions
- Use descriptive test names that explain what is being tested

### Test Data
- Always clean up test data after each test
- Use factories for consistent test data creation
- Create minimal data needed for each test
- Avoid dependencies between tests

### Assertions
- Use specific assertions with clear error messages
- Test both positive and negative scenarios
- Verify not just status codes but also response content
- Test edge cases and boundary conditions

### Performance
- Tests should run quickly (< 5 seconds for the full suite)
- Use SQLite for fast database operations
- Minimize external service calls
- Run tests in parallel when possible

## Troubleshooting

### Common Issues

1. **Database Lock Errors**
   - Ensure proper cleanup in test teardown
   - Check for unclosed database connections

2. **Port Already in Use**
   - Tests use port 3010 by default
   - Ensure no other services are using this port

3. **Test Data Conflicts**
   - Each test should clean up its data
   - Use unique identifiers for test data

4. **Authentication Failures**
   - Verify JWT secret is set correctly
   - Check token expiration times
   - Ensure user exists before attempting login

### Debug Mode
Set environment variable for verbose logging:
```bash
export DEBUG=true
go test ./tests/... -v
```

## Contributing

When adding new tests:
1. Follow the existing test structure and naming conventions
2. Add appropriate cleanup for any test data created
3. Include both positive and negative test cases
4. Update this README if adding new test categories
5. Ensure tests are independent and can run in any order
