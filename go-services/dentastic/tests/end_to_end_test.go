package tests

import (
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

func TestEndToEndWorkflow(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Complete Clinic Workflow", func(t *testing.T) {
		// Get subscription plan for clinic creation
		plan := new(lib.DentalClinicSubscriptionPlan)
		if err := testData.db.First(plan).Error; err != nil {
			t.Fatalf("Failed to get subscription plan: %v", err)
		}

		// Step 1: Register a new clinic
		t.Log("Step 1: Registering new clinic...")
		clinicReq := ClinicCreateRequest{
			DisplayName:      "Complete Workflow Clinic",
			Username:         "workflowadmin",
			Name:             "Dr. Workflow Admin",
			Email:            "<EMAIL>",
			Password:         "securepassword123",
			PhoneNumber:      "01234567999",
			SystemName:       "workflowclinic",
			SubscriptionPlan: plan.ID,
		}

		resp := client.Request("POST", "/clinics", clinicReq, nil)
		if resp.Code != 201 {
			t.Fatalf("Clinic registration failed with status %d: %s", resp.Code, resp.Body.String())
		}

		result := client.AssertJSONResponse(resp, map[string]interface{}{})
		registrationToken, ok := result["token"].(string)
		if !ok || registrationToken == "" {
			t.Fatal("Registration token not returned")
		}
		t.Logf("✅ Clinic registered successfully, token: %s...", registrationToken[:20])

		// Step 2: Login with the registered credentials
		t.Log("Step 2: Logging in with registered credentials...")
		loginReq := LoginRequest{
			ClinicName: "workflowclinic",
			Username:   "workflowadmin",
			Password:   "securepassword123",
		}

		loginResp := client.Request("POST", "/login", loginReq, nil)
		if loginResp.Code != 200 {
			t.Fatalf("Login failed with status %d: %s", loginResp.Code, loginResp.Body.String())
		}

		loginResult := client.AssertJSONResponse(loginResp, map[string]interface{}{})
		loginToken, ok := loginResult["token"].(string)
		if !ok || loginToken == "" {
			t.Fatal("Login token not returned")
		}
		t.Logf("✅ Login successful, token: %s...", loginToken[:20])

		// Step 3: Verify clinic was created in database
		t.Log("Step 3: Verifying clinic in database...")
		var clinic lib.DentalClinic
		if err := testData.db.Where("name = ?", "workflowclinic").First(&clinic).Error; err != nil {
			t.Fatalf("Failed to find created clinic: %v", err)
		}

		if clinic.DisplayName != "Complete Workflow Clinic" {
			t.Errorf("Expected display name 'Complete Workflow Clinic', got '%s'", clinic.DisplayName)
		}
		t.Logf("✅ Clinic verified in database: %s (ID: %s)", clinic.DisplayName, clinic.ID)

		// Step 4: Verify user was created
		t.Log("Step 4: Verifying user in database...")
		var user lib.DentalClinicUser
		if err := testData.db.Where("clinic_id = ? AND username = ?", clinic.ID, "workflowadmin").First(&user).Error; err != nil {
			t.Fatalf("Failed to find created user: %v", err)
		}

		if user.Name != "Dr. Workflow Admin" {
			t.Errorf("Expected user name 'Dr. Workflow Admin', got '%s'", user.Name)
		}

		if user.Role != lib.DentalClinicRoleMaster {
			t.Errorf("Expected user role 'master', got '%s'", user.Role)
		}
		t.Logf("✅ User verified in database: %s (Role: %s)", user.Name, user.Role)

		// Step 5: Test case-insensitive login
		t.Log("Step 5: Testing case-insensitive login...")
		caseInsensitiveReq := LoginRequest{
			ClinicName: "WORKFLOWCLINIC", // Uppercase
			Username:   "WORKFLOWADMIN",  // Uppercase
			Password:   "securepassword123",
		}

		caseResp := client.Request("POST", "/login", caseInsensitiveReq, nil)
		if caseResp.Code != 200 {
			t.Errorf("Case-insensitive login failed with status %d: %s", caseResp.Code, caseResp.Body.String())
		} else {
			t.Log("✅ Case-insensitive login successful")
		}

		// Step 6: Test invalid login attempts
		t.Log("Step 6: Testing invalid login attempts...")
		invalidTests := []struct {
			name    string
			request LoginRequest
			status  int
		}{
			{
				name: "Wrong Password",
				request: LoginRequest{
					ClinicName: "workflowclinic",
					Username:   "workflowadmin",
					Password:   "wrongpassword",
				},
				status: 401,
			},
			{
				name: "Wrong Username",
				request: LoginRequest{
					ClinicName: "workflowclinic",
					Username:   "wronguser",
					Password:   "securepassword123",
				},
				status: 401,
			},
			{
				name: "Wrong Clinic",
				request: LoginRequest{
					ClinicName: "wrongclinic",
					Username:   "workflowadmin",
					Password:   "securepassword123",
				},
				status: 404,
			},
		}

		for _, test := range invalidTests {
			resp := client.Request("POST", "/login", test.request, nil)
			if resp.Code != test.status {
				t.Errorf("%s: Expected status %d, got %d", test.name, test.status, resp.Code)
			} else {
				t.Logf("✅ %s correctly rejected", test.name)
			}
		}

		// Step 7: Test duplicate registration prevention
		t.Log("Step 7: Testing duplicate registration prevention...")
		duplicateReq := clinicReq
		duplicateReq.SystemName = "differentclinic"
		duplicateReq.DisplayName = "Different Clinic"
		// Keep same email to test duplicate prevention

		dupResp := client.Request("POST", "/clinics", duplicateReq, nil)
		if dupResp.Code == 201 {
			t.Error("Duplicate email registration should have been rejected")
		} else {
			t.Log("✅ Duplicate email registration correctly rejected")
		}

		t.Log("🎉 Complete workflow test passed successfully!")
	})

	t.Run("Multiple Clinics Workflow", func(t *testing.T) {
		// Get subscription plan
		plan := new(lib.DentalClinicSubscriptionPlan)
		if err := testData.db.First(plan).Error; err != nil {
			t.Fatalf("Failed to get subscription plan: %v", err)
		}

		// Create multiple clinics
		clinics := []struct {
			name     string
			email    string
			phone    string
			system   string
			username string
		}{
			{"Clinic Alpha", "<EMAIL>", "01111111111", "alpha", "alphaadmin"},
			{"Clinic Beta", "<EMAIL>", "01222222222", "beta", "betaadmin"},
			{"Clinic Gamma", "<EMAIL>", "01333333333", "gamma", "gammaadmin"},
		}

		tokens := make([]string, len(clinics))

		// Register all clinics
		for i, clinic := range clinics {
			t.Logf("Registering %s...", clinic.name)
			
			clinicReq := ClinicCreateRequest{
				DisplayName:      clinic.name,
				Username:         clinic.username,
				Name:             "Dr. " + clinic.username,
				Email:            clinic.email,
				Password:         "password123",
				PhoneNumber:      clinic.phone,
				SystemName:       clinic.system,
				SubscriptionPlan: plan.ID,
			}

			resp := client.Request("POST", "/clinics", clinicReq, nil)
			if resp.Code != 201 {
				t.Fatalf("Failed to register %s: %s", clinic.name, resp.Body.String())
			}

			result := client.AssertJSONResponse(resp, map[string]interface{}{})
			token, ok := result["token"].(string)
			if !ok {
				t.Fatalf("No token returned for %s", clinic.name)
			}
			tokens[i] = token
		}

		// Test login for all clinics
		for _, clinic := range clinics {
			t.Logf("Testing login for %s...", clinic.name)
			
			loginReq := LoginRequest{
				ClinicName: clinic.system,
				Username:   clinic.username,
				Password:   "password123",
			}

			resp := client.Request("POST", "/login", loginReq, nil)
			if resp.Code != 200 {
				t.Errorf("Login failed for %s: %s", clinic.name, resp.Body.String())
			}
		}

		// Verify all clinics exist in database
		var count int64
		testData.db.Model(&lib.DentalClinic{}).Where("name IN ?", []string{"alpha", "beta", "gamma"}).Count(&count)
		if count != 3 {
			t.Errorf("Expected 3 clinics in database, found %d", count)
		}

		t.Log("✅ Multiple clinics workflow completed successfully")
	})
}
