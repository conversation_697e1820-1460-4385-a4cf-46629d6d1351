package tests

import (
	"database/sql"
	"testing"
	"time"

	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
	"gorm.io/gorm"
)

func TestDatabaseIntegration(t *testing.T) {
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Database Connection", func(t *testing.T) {
		// Test basic database connectivity
		sqlDB, err := server.Db.DB()
		if err != nil {
			t.Fatalf("Failed to get underlying sql.DB: %v", err)
		}

		if err := sqlDB.Ping(); err != nil {
			t.Fatalf("Database ping failed: %v", err)
		}
	})

	t.Run("Model Creation and Relationships", func(t *testing.T) {
		// Create a complete clinic with all relationships
		clinic, user, merchant := testData.CreateTestClinic("dbtest")

		// Verify merchant was created
		var fetchedMerchant models.Merchant
		if err := server.Db.First(&fetchedMerchant, merchant.ID).Error; err != nil {
			t.Fatalf("Failed to fetch merchant: %v", err)
		}

		if fetchedMerchant.Email != merchant.Email {
			t.<PERSON>("Expected merchant email %s, got %s", merchant.Email, fetchedMerchant.Email)
		}

		// Verify clinic was created with proper relationships
		var fetchedClinic lib.DentalClinic
		if err := server.Db.Preload("SubscriptionPlan").First(&fetchedClinic, clinic.ID).Error; err != nil {
			t.Fatalf("Failed to fetch clinic: %v", err)
		}

		if fetchedClinic.MerchantID != merchant.ID {
			t.Errorf("Expected clinic merchant ID %s, got %s", merchant.ID, fetchedClinic.MerchantID)
		}

		if fetchedClinic.SubscriptionPlan.Name == "" {
			t.Error("Subscription plan relationship not loaded")
		}

		// Verify user was created with clinic relationship
		var fetchedUser lib.DentalClinicUser
		if err := server.Db.Preload("Clinic").First(&fetchedUser, user.ID).Error; err != nil {
			t.Fatalf("Failed to fetch user: %v", err)
		}

		if fetchedUser.ClinicID != clinic.ID {
			t.Errorf("Expected user clinic ID %s, got %s", clinic.ID, fetchedUser.ClinicID)
		}

		if fetchedUser.Clinic.Name != clinic.Name {
			t.Errorf("Expected clinic name %s, got %s", clinic.Name, fetchedUser.Clinic.Name)
		}
	})

	t.Run("Patient Management", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("patienttest")

		// Create patient
		patient := testData.CreateTestPatient(clinic.ID, "John Doe", "01234567890")

		// Verify patient was created
		var fetchedPatient lib.DentalClinicPatient
		if err := server.Db.First(&fetchedPatient, patient.ID).Error; err != nil {
			t.Fatalf("Failed to fetch patient: %v", err)
		}

		if fetchedPatient.Name != "John Doe" {
			t.Errorf("Expected patient name 'John Doe', got '%s'", fetchedPatient.Name)
		}

		// Test patient update
		fetchedPatient.Address = "Updated Address"
		if err := server.Db.Save(&fetchedPatient).Error; err != nil {
			t.Fatalf("Failed to update patient: %v", err)
		}

		// Verify update
		var updatedPatient lib.DentalClinicPatient
		if err := server.Db.First(&updatedPatient, patient.ID).Error; err != nil {
			t.Fatalf("Failed to fetch updated patient: %v", err)
		}

		if updatedPatient.Address != "Updated Address" {
			t.Errorf("Expected address 'Updated Address', got '%s'", updatedPatient.Address)
		}

		// Test soft delete
		if err := server.Db.Delete(&updatedPatient).Error; err != nil {
			t.Fatalf("Failed to soft delete patient: %v", err)
		}

		// Verify soft delete (should not find without Unscoped)
		var deletedPatient lib.DentalClinicPatient
		err := server.Db.First(&deletedPatient, patient.ID).Error
		if err == nil {
			t.Error("Expected patient to be soft deleted")
		}

		// Should find with Unscoped
		if err := server.Db.Unscoped().First(&deletedPatient, patient.ID).Error; err != nil {
			t.Fatalf("Failed to fetch soft deleted patient: %v", err)
		}

		if deletedPatient.DeletedAt.Time.IsZero() {
			t.Error("Expected DeletedAt to be set")
		}
	})

	t.Run("Appointment Management", func(t *testing.T) {
		clinic, user, _ := testData.CreateTestClinic("appointmenttest")
		patient := testData.CreateTestPatient(clinic.ID, "Jane Doe", "01987654321")

		// Create appointment
		appointment := &lib.DentalClinicAppointment{
			ClinicID:    clinic.ID,
			PatientID:   patient.ID,
			DentistID:   user.ID,
			Date:        time.Now().AddDate(0, 0, 1).Format("2006-01-02"),
			Time:        "10:00",
			Duration:    60,
			Status:      lib.DentalClinicAppointmentStatusScheduled,
			Notes:       "Regular checkup",
		}

		if err := server.Db.Create(appointment).Error; err != nil {
			t.Fatalf("Failed to create appointment: %v", err)
		}

		// Verify appointment with relationships
		var fetchedAppointment lib.DentalClinicAppointment
		if err := server.Db.Preload("Patient").Preload("Dentist").First(&fetchedAppointment, appointment.ID).Error; err != nil {
			t.Fatalf("Failed to fetch appointment: %v", err)
		}

		if fetchedAppointment.Patient.Name != patient.Name {
			t.Errorf("Expected patient name %s, got %s", patient.Name, fetchedAppointment.Patient.Name)
		}

		if fetchedAppointment.Dentist.Name != user.Name {
			t.Errorf("Expected dentist name %s, got %s", user.Name, fetchedAppointment.Dentist.Name)
		}

		// Test appointment status update
		fetchedAppointment.Status = lib.DentalClinicAppointmentStatusCompleted
		if err := server.Db.Save(&fetchedAppointment).Error; err != nil {
			t.Fatalf("Failed to update appointment status: %v", err)
		}

		// Verify status update
		var updatedAppointment lib.DentalClinicAppointment
		if err := server.Db.First(&updatedAppointment, appointment.ID).Error; err != nil {
			t.Fatalf("Failed to fetch updated appointment: %v", err)
		}

		if updatedAppointment.Status != lib.DentalClinicAppointmentStatusCompleted {
			t.Errorf("Expected status %s, got %s", lib.DentalClinicAppointmentStatusCompleted, updatedAppointment.Status)
		}
	})

	t.Run("Transaction Rollback", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("transactiontest")

		// Test transaction rollback
		err := server.Db.Transaction(func(tx *gorm.DB) error {
			// Create a patient
			patient := &lib.DentalClinicPatient{
				Name:        "Transaction Test Patient",
				PhoneNumber: "01111111111",
				ClinicID:    clinic.ID,
			}

			if err := tx.Create(patient).Error; err != nil {
				return err
			}

			// Create an appointment
			appointment := &lib.DentalClinicAppointment{
				ClinicID:  clinic.ID,
				PatientID: patient.ID,
				Date:      time.Now().Format("2006-01-02"),
				Time:      "14:00",
				Duration:  30,
				Status:    lib.DentalClinicAppointmentStatusScheduled,
			}

			if err := tx.Create(appointment).Error; err != nil {
				return err
			}

			// Force rollback by returning an error
			return gorm.ErrInvalidTransaction
		})

		if err == nil {
			t.Fatal("Expected transaction to fail")
		}

		// Verify that neither patient nor appointment were created
		var patientCount int64
		server.Db.Model(&lib.DentalClinicPatient{}).Where("name = ?", "Transaction Test Patient").Count(&patientCount)
		if patientCount != 0 {
			t.Errorf("Expected patient count 0 after rollback, got %d", patientCount)
		}

		var appointmentCount int64
		server.Db.Model(&lib.DentalClinicAppointment{}).Where("clinic_id = ?", clinic.ID).Count(&appointmentCount)
		if appointmentCount != 0 {
			t.Errorf("Expected appointment count 0 after rollback, got %d", appointmentCount)
		}
	})

	t.Run("Foreign Key Constraints", func(t *testing.T) {
		clinic, user, _ := testData.CreateTestClinic("fktest")

		// Try to create appointment with invalid patient ID
		appointment := &lib.DentalClinicAppointment{
			ClinicID:  clinic.ID,
			PatientID: "invalid-patient-id",
			DentistID: user.ID,
			Date:      time.Now().Format("2006-01-02"),
			Time:      "15:00",
			Duration:  45,
			Status:    lib.DentalClinicAppointmentStatusScheduled,
		}

		err := server.Db.Create(appointment).Error
		if err == nil {
			t.Error("Expected foreign key constraint error for invalid patient ID")
		}

		// Try to create user with invalid clinic ID
		invalidUser := &lib.DentalClinicUser{
			Name:     "Invalid User",
			Username: "invaliduser",
			Password: "password",
			Role:     lib.DentalClinicRoleBasic,
			ClinicID: "invalid-clinic-id",
		}

		err = server.Db.Create(invalidUser).Error
		if err == nil {
			t.Error("Expected foreign key constraint error for invalid clinic ID")
		}
	})

	t.Run("Null Value Handling", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("nulltest")

		// Create patient with null birthdate
		patient := &lib.DentalClinicPatient{
			Name:        "Null Test Patient",
			PhoneNumber: "01222222222",
			ClinicID:    clinic.ID,
			Birthdate:   sql.NullString{Valid: false}, // Null birthdate
		}

		if err := server.Db.Create(patient).Error; err != nil {
			t.Fatalf("Failed to create patient with null birthdate: %v", err)
		}

		// Verify null value was stored correctly
		var fetchedPatient lib.DentalClinicPatient
		if err := server.Db.First(&fetchedPatient, patient.ID).Error; err != nil {
			t.Fatalf("Failed to fetch patient: %v", err)
		}

		if fetchedPatient.Birthdate.Valid {
			t.Error("Expected birthdate to be null")
		}

		// Update with valid birthdate
		fetchedPatient.Birthdate = sql.NullString{
			String: "1990-01-01",
			Valid:  true,
		}

		if err := server.Db.Save(&fetchedPatient).Error; err != nil {
			t.Fatalf("Failed to update patient birthdate: %v", err)
		}

		// Verify update
		var updatedPatient lib.DentalClinicPatient
		if err := server.Db.First(&updatedPatient, patient.ID).Error; err != nil {
			t.Fatalf("Failed to fetch updated patient: %v", err)
		}

		if !updatedPatient.Birthdate.Valid || updatedPatient.Birthdate.String != "1990-01-01" {
			t.Errorf("Expected birthdate '1990-01-01', got '%s' (valid: %t)",
				updatedPatient.Birthdate.String, updatedPatient.Birthdate.Valid)
		}
	})
}
