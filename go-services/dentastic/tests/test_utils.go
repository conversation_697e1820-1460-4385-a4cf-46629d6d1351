package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Global test variables - these are set in main_test.go
var (
	testApp    *fiber.App
	testServer *config.Server
)

// TestClient wraps the Fiber app for easier testing
type TestClient struct {
	app *fiber.App
	t   *testing.T
}

// NewTestClient creates a new test client
func NewTestClient(t *testing.T) *TestClient {
	return &TestClient{
		app: GetTestApp(),
		t:   t,
	}
}

// GetTestApp returns the test app instance
func GetTestApp() *fiber.App {
	// app variable is defined in main_test.go and available globally
	return app
}

// GetTestDB returns the test database instance
func GetTestDB() *gorm.DB {
	// server variable is defined in main_test.go and available globally
	return server.Db
}

// Request makes an HTTP request and returns the response
func (tc *TestClient) Request(method, path string, body interface{}, headers map[string]string) *TestResponse {
	var bodyReader io.Reader

	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			tc.t.Fatalf("Failed to marshal request body: %v", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	req := httptest.NewRequest(method, path, bodyReader)

	// Set default content type for POST/PUT/PATCH requests
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := tc.app.Test(req)
	if err != nil {
		tc.t.Fatalf("Request failed: %v", err)
	}

	// Read response body
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		tc.t.Fatalf("Failed to read response body: %v", err)
	}

	return &TestResponse{
		Code:    resp.StatusCode,
		Body:    bytes.NewBuffer(bodyBytes),
		Headers: resp.Header,
	}
}

// TestResponse wraps HTTP response for easier testing
type TestResponse struct {
	Code    int
	Body    *bytes.Buffer
	Headers map[string][]string
}

// RequestWithAuth makes an authenticated request
func (tc *TestClient) RequestWithAuth(method, path string, body interface{}, token string) *TestResponse {
	headers := map[string]string{
		"Token": token,
	}
	return tc.Request(method, path, body, headers)
}

// AssertStatusCode checks if the response has the expected status code
func (tc *TestClient) AssertStatusCode(resp *TestResponse, expected int) {
	if resp.Code != expected {
		tc.t.Errorf("Expected status code %d, got %d. Response body: %s", expected, resp.Code, resp.Body.String())
	}
}

// AssertJSONResponse checks if the response is valid JSON and optionally validates fields
func (tc *TestClient) AssertJSONResponse(resp *TestResponse, expectedFields map[string]interface{}) map[string]interface{} {
	var result map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &result)
	if err != nil {
		tc.t.Fatalf("Response is not valid JSON: %v. Body: %s", err, resp.Body.String())
	}

	for field, expectedValue := range expectedFields {
		if actualValue, exists := result[field]; !exists {
			tc.t.Errorf("Expected field '%s' not found in response", field)
		} else if actualValue != expectedValue {
			tc.t.Errorf("Expected field '%s' to be '%v', got '%v'", field, expectedValue, actualValue)
		}
	}

	return result
}

// TestData contains test data factories
type TestData struct {
	db *gorm.DB
	t  *testing.T
}

// NewTestData creates a new test data factory
func NewTestData(t *testing.T) *TestData {
	return &TestData{
		db: GetTestDB(),
		t:  t,
	}
}

// CreateTestClinic creates a test clinic with a master user
func (td *TestData) CreateTestClinic(name string) (*lib.DentalClinic, *lib.DentalClinicUser, *models.Merchant) {
	// Get test subscription plan
	plan := new(lib.DentalClinicSubscriptionPlan)
	if err := td.db.First(plan).Error; err != nil {
		td.t.Fatalf("Failed to get test subscription plan: %v", err)
	}

	// Create merchant with properly hashed password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password"), 10)
	if err != nil {
		td.t.Fatalf("Failed to hash merchant password: %v", err)
	}

	merchant := &models.Merchant{
		DisplayName: fmt.Sprintf("%s Clinic", name),
		SystemName:  strings.ToLower(name),
		Email:       fmt.Sprintf("%<EMAIL>", strings.ToLower(name)),
		PhoneNumber: fmt.Sprintf("01%08d", time.Now().Unix()%100000000),
		Type:        models.MerchantTypeDentalClinic,
		Password:    string(hashedPassword),
	}

	if err := td.db.Create(merchant).Error; err != nil {
		td.t.Fatalf("Failed to create test merchant: %v", err)
	}

	// Create clinic
	clinic := &lib.DentalClinic{
		DisplayName:           fmt.Sprintf("%s Clinic", name),
		Name:                  strings.ToLower(name),
		Currency:              "EGP",
		Country:               "Egypt",
		BranchCount:           1,
		SubscriptionRenewedAt: time.Now().Format(time.RFC3339),
		SubscriptionEndDate:   time.Now().AddDate(0, 1, 0).Format(time.RFC3339),
	}
	clinic.MerchantID = merchant.ID
	clinic.SubscriptionPlanID.String = plan.ID
	clinic.SubscriptionPlanID.Valid = true

	if err := td.db.Create(clinic).Error; err != nil {
		td.t.Fatalf("Failed to create test clinic: %v", err)
	}

	// Create master user with properly hashed password
	userHashedPassword, err := bcrypt.GenerateFromPassword([]byte("password"), 10)
	if err != nil {
		td.t.Fatalf("Failed to hash user password: %v", err)
	}

	user := &lib.DentalClinicUser{
		Name:      fmt.Sprintf("%s Admin", name),
		Username:  fmt.Sprintf("%s_admin", strings.ToLower(name)),
		Password:  string(userHashedPassword),
		Role:      lib.DentalClinicRoleMaster,
		IsDentist: true,
		ClinicID:  clinic.ID,
	}

	if err := td.db.Create(user).Error; err != nil {
		td.t.Fatalf("Failed to create test user: %v", err)
	}

	// Load relationships
	if err := td.db.Preload("Clinic").First(user, user.ID).Error; err != nil {
		td.t.Fatalf("Failed to load user with clinic: %v", err)
	}

	return clinic, user, merchant
}

// CreateTestUser creates a test user for an existing clinic
func (td *TestData) CreateTestUser(clinicID, name, username string, role lib.DentalClinicRole) *lib.DentalClinicUser {
	// Hash password properly
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password"), 10)
	if err != nil {
		td.t.Fatalf("Failed to hash user password: %v", err)
	}

	user := &lib.DentalClinicUser{
		Name:      name,
		Username:  username,
		Password:  string(hashedPassword),
		Role:      role,
		IsDentist: role == lib.DentalClinicRoleMaster || role == lib.DentalClinicRoleAdmin,
		ClinicID:  clinicID,
	}

	if err := td.db.Create(user).Error; err != nil {
		td.t.Fatalf("Failed to create test user: %v", err)
	}

	return user
}

// CreateTestPatient creates a test patient for a clinic
func (td *TestData) CreateTestPatient(clinicID, name, phoneNumber string) *lib.DentalClinicPatient {
	patient := &lib.DentalClinicPatient{
		Name:        name,
		PhoneNumber: phoneNumber,
		Email:       fmt.Sprintf("%<EMAIL>", strings.ToLower(strings.ReplaceAll(name, " ", ""))),
		Address:     "Test Address",
		ClinicID:    clinicID,
	}

	if err := td.db.Create(patient).Error; err != nil {
		td.t.Fatalf("Failed to create test patient: %v", err)
	}

	return patient
}

// CleanupTestData removes all test data from the database
func (td *TestData) CleanupTestData() {
	// Delete in reverse order of dependencies
	tables := []string{
		"dental_clinic_users",
		"dental_clinic_patients",
		"dental_clinics",
		"merchants",
	}

	for _, table := range tables {
		if err := td.db.Exec(fmt.Sprintf("DELETE FROM %s", table)).Error; err != nil {
			td.t.Logf("Warning: Failed to cleanup table %s: %v", table, err)
		}
	}
}

// LoginRequest represents a login request
type LoginRequest struct {
	ClinicName string `json:"clinicName"`
	Username   string `json:"username"`
	Password   string `json:"password"`
}

// ClinicCreateRequest represents a clinic creation request
type ClinicCreateRequest struct {
	DisplayName      string `json:"displayName"`
	Username         string `json:"username"`
	Name             string `json:"name"`
	Email            string `json:"email"`
	Password         string `json:"password"`
	PhoneNumber      string `json:"phoneNumber"`
	SystemName       string `json:"systemName"`
	SubscriptionPlan string `json:"subscriptionPlan"`
}

// GetAuthToken performs login and returns the JWT token
func (tc *TestClient) GetAuthToken(clinicName, username, password string) string {
	loginReq := LoginRequest{
		ClinicName: clinicName,
		Username:   username,
		Password:   password,
	}

	resp := tc.Request("POST", "/login", loginReq, nil)
	if resp.Code != 200 {
		tc.t.Fatalf("Login failed with status %d: %s", resp.Code, resp.Body.String())
	}

	var result map[string]interface{}
	if err := json.Unmarshal(resp.Body.Bytes(), &result); err != nil {
		tc.t.Fatalf("Failed to parse login response: %v", err)
	}

	token, ok := result["token"].(string)
	if !ok {
		tc.t.Fatalf("Token not found in login response")
	}

	return token
}
