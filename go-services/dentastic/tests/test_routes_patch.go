package tests

import (
	"encoding/json"
	"errors"
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/kataras/jwt"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/core/responses"
	"gitlab.com/payrows/dentastic/lib"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// PatchLoginHandler creates a SQLite-compatible version of the login handler
func PatchLoginHandler(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		ClinicName string `json:"clinicName"`
		Username   string `json:"username"`
		Password   string `json:"password"`
	}
	return func(c *fiber.Ctx) error {
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		clinicName := strings.TrimSpace(body.ClinicName)
		username := strings.TrimSpace(body.Username)
		if len(clinicName) == 0 {
			return responses.Send400(c, "Clinic name missing")
		}
		if len(username) == 0 {
			return responses.Send400(c, "Username missing")
		}
		if len(body.Password) == 0 {
			return responses.Send400(c, "Password missing")
		}

		// Use SQLite-compatible queries
		clinic := new(lib.DentalClinic)

		// SQLite-compatible case-insensitive search
		if err = server.Db.Where("name LIKE ? COLLATE NOCASE", clinicName).Take(clinic).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Clinic not found"})
			}
			panic(err)
		}

		user := new(lib.DentalClinicUser)
		if err = server.Db.Where("clinic_id = ? AND username LIKE ? COLLATE NOCASE", clinic.ID, username).Take(user).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return responses.Send401(c)
			}
			panic(err)
		}

		if string(user.Role) == string(lib.DentalClinicRoleExternal) {
			log.Println("User is external")
			return c.Status(401).JSON(map[string]string{"error": "Cannot log in as an external user"})
		}

		err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(body.Password))
		if err != nil {
			return responses.Send401(c)
		}

		tokenExpiry := time.Now().Add(7 * 24 * time.Hour)
		log.Println(clinic.SubscriptionRenewedAt)
		subscriptionRenewedAt, err := time.Parse(time.RFC3339, clinic.SubscriptionRenewedAt)
		if err != nil {
			log.Println("failed to get datetime from date string in subscription renewed at. Giving customer free access:", err)
			subscriptionRenewedAt = time.Now()
		}

		token, err := jwt.Sign(server.JWTAlgorithm, server.JWTSecret, map[string]interface{}{
			"merchant":              clinic.MerchantID,
			"id":                    user.ID,
			"clinicId":              clinic.ID,
			"type":                  models.MerchantTypeDentalClinic,
			"role":                  string(user.Role),
			"subscriptionRenewedAt": subscriptionRenewedAt.Unix(),
			"exp":                   tokenExpiry.Unix(),
		})
		if err != nil {
			panic(err)
		}

		return c.JSON(map[string]string{"token": string(token)})
	}
}

// InitializeForTesting creates a test-specific initialization with SQLite-compatible routes
func InitializeForTesting(initConfig lib.IntializationConfig) (*fiber.App, *config.Server) {
	// Create a new app without calling the standard initialization
	app := fiber.New(fiber.Config{
		ErrorHandler: config.DefaultErrorHandler,
	})

	server := new(config.Server)
	server.App = app

	// Initialize database and other components manually
	server.JWTAlgorithm = jwt.HS256
	server.JWTSecret = []byte("test_jwt_secret_for_integration_tests")

	// Initialize database
	db, err := lib.InitializeDB()
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	server.Db = db

	// Run migrations if needed
	if initConfig.ShouldMigrate {
		err = db.AutoMigrate(
			&lib.DentalClinicSubscriptionPlan{},
			&lib.DentalClinicPatientNotificationConfig{},
			&lib.DentalClinic{},
			&lib.DentalClinicUser{},
			&lib.DentalClinicBranch{},
			&lib.DentalClinicPatient{},
			&lib.DentalClinicPatientFile{},
			&lib.DentalClinicAppointment{},
			&lib.DentalClinicExpense{},
			&lib.DentalClinicInventoryItem{},
			&lib.DentalClinicInventoryItemTransaction{},
			&lib.DentalClinicSpecialityTemplate{},
			&lib.DentalClinicProcedureTemplate{},
			&lib.DentalClinicVisit{},
			&lib.DentalClinicVisitProcedure{},
			&lib.DentalClinicPatientPayment{},
			&lib.DentalClinicPatientPaymentLink{},
			&lib.DentalClinicPatientGroup{},
			&lib.DentalClinicPatientGroupMember{},
			&lib.DentalClinicLab{},
			&lib.DentalClinicLabRequest{},
			&lib.DentalClinicInsuranceCompany{},
			&lib.DentalClinicInsuranceCompanyClaim{},
			&lib.DentalClinicInsuranceCompanyClaimItem{},
			&lib.DentalClinicCustomPaymentMethod{},
			&lib.DentalClinicCommunication{},
			&lib.DentalClinicUserOpenAILink{},
			&lib.DentalClinicSMSQuota{},
			&lib.DentalClinicTimesheetEntry{},
			&lib.DentalClinicUserOTP{},
			&lib.DentalClinicBirthdayWishConfig{},
		)
		if err != nil {
			log.Fatalf("Migration failed: %v", err)
		}
	}

	// Register SQLite-compatible routes
	RegisterTestRoutes(server, app)

	return app, server
}

// RegisterTestRoutes registers SQLite-compatible routes for testing
func RegisterTestRoutes(server *config.Server, app *fiber.App) error {
	// Copy the middleware setup from the original RegisterRoutes
	app.Use(logger.New(
		logger.Config{
			Format: "[${time}] ${status} - ${latency} ${method} ${url}\n",
		},
	))

	app.Use(recover.New(recover.Config{
		EnableStackTrace: true,
	}))

	// Register SQLite-compatible login handler
	app.Post("/login", PatchLoginHandler(server))

	// Register test-specific routes that bypass external services
	app.Post("/register", PatchClinicCreateHandler(server))
	app.Post("/clinics", PatchClinicCreateHandler(server))

	// Add essential routes for testing (without middleware for simplicity)
	app.Get("/users/me", lib.HandleUserProfileGet(server))

	return nil
}

// isValidEmail validates email format using a simple regex
func isValidEmail(email string) bool {
	// Simple email validation regex
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	matched, _ := regexp.MatchString(emailRegex, email)
	return matched
}

// PatchClinicCreateHandler creates a test-specific clinic creation handler that bypasses Stripe
func PatchClinicCreateHandler(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		DisplayName      string `json:"displayName"`
		Username         string `json:"username"`
		Name             string `json:"name"`
		Email            string `json:"email"`
		Password         string `json:"password"`
		PhoneNumber      string `json:"phoneNumber"`
		SystemName       string `json:"systemName"`
		SubscriptionPlan string `json:"subscriptionPlan"`
	}

	return func(c *fiber.Ctx) error {
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		// Basic validation
		if strings.TrimSpace(body.DisplayName) == "" {
			return responses.Send400(c, "Display name is required")
		}
		if strings.TrimSpace(body.Email) == "" {
			return responses.Send400(c, "Email is required")
		}
		// Validate email format
		if !isValidEmail(body.Email) {
			return responses.Send400(c, "Invalid email format")
		}
		if strings.TrimSpace(body.Password) == "" {
			return responses.Send400(c, "Password is required")
		}
		if len(body.Password) < 8 {
			return responses.Send400(c, "Password must be at least 8 characters")
		}
		if strings.TrimSpace(body.PhoneNumber) == "" {
			return responses.Send400(c, "Phone number is required")
		}
		if strings.TrimSpace(body.SystemName) == "" {
			return responses.Send400(c, "System name is required")
		}
		if strings.TrimSpace(body.Username) == "" {
			return responses.Send400(c, "Username is required")
		}

		// Validate subscription plan exists
		var plan lib.DentalClinicSubscriptionPlan
		if err := server.Db.Where("id = ?", body.SubscriptionPlan).First(&plan).Error; err != nil {
			return responses.Send400(c, "Invalid subscription plan")
		}

		// Check for duplicate email
		var existingMerchant models.Merchant
		if err := server.Db.Where("email = ?", body.Email).First(&existingMerchant).Error; err == nil {
			return responses.Send409(c, "Email already exists")
		}

		// Check for duplicate phone
		if err := server.Db.Where("phone_number = ?", body.PhoneNumber).First(&existingMerchant).Error; err == nil {
			return responses.Send409(c, "Phone number already exists")
		}

		// Check for duplicate system name
		var existingClinic lib.DentalClinic
		if err := server.Db.Where("name = ?", body.SystemName).First(&existingClinic).Error; err == nil {
			return responses.Send409(c, "System name already exists")
		}

		// Hash password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(body.Password), 10)
		if err != nil {
			return responses.Send500(c)
		}

		// Create merchant (bypass Stripe)
		merchant := &models.Merchant{
			DisplayName: body.DisplayName,
			SystemName:  body.SystemName,
			Email:       body.Email,
			PhoneNumber: body.PhoneNumber,
			Type:        models.MerchantTypeDentalClinic,
			Password:    string(hashedPassword),
			// Skip Stripe customer creation for testing
		}

		if err := server.Db.Create(merchant).Error; err != nil {
			return responses.Send500(c)
		}

		// Create clinic
		clinic := &lib.DentalClinic{
			DisplayName:           body.DisplayName,
			Name:                  body.SystemName,
			Currency:              "EGP",
			Country:               "Egypt",
			BranchCount:           1,
			SubscriptionRenewedAt: time.Now().Format(time.RFC3339),
			SubscriptionEndDate:   time.Now().AddDate(0, 1, 0).Format(time.RFC3339),
		}
		clinic.MerchantID = merchant.ID
		clinic.SubscriptionPlanID.String = plan.ID
		clinic.SubscriptionPlanID.Valid = true

		if err := server.Db.Create(clinic).Error; err != nil {
			return responses.Send500(c)
		}

		// Create master user
		userHashedPassword, err := bcrypt.GenerateFromPassword([]byte(body.Password), 10)
		if err != nil {
			return responses.Send500(c)
		}

		user := &lib.DentalClinicUser{
			Name:      body.Name,
			Username:  body.Username,
			Password:  string(userHashedPassword),
			Role:      lib.DentalClinicRoleMaster,
			IsDentist: true,
			ClinicID:  clinic.ID,
		}

		if err := server.Db.Create(user).Error; err != nil {
			return responses.Send500(c)
		}

		// Generate JWT token
		tokenExpiry := time.Now().Add(7 * 24 * time.Hour)
		subscriptionRenewedAt, _ := time.Parse(time.RFC3339, clinic.SubscriptionRenewedAt)

		token, err := jwt.Sign(server.JWTAlgorithm, server.JWTSecret, map[string]interface{}{
			"merchant":              clinic.MerchantID,
			"id":                    user.ID,
			"clinicId":              clinic.ID,
			"type":                  models.MerchantTypeDentalClinic,
			"role":                  string(user.Role),
			"subscriptionRenewedAt": subscriptionRenewedAt.Unix(),
			"exp":                   tokenExpiry.Unix(),
		})
		if err != nil {
			return responses.Send500(c)
		}

		return c.Status(201).JSON(map[string]string{"token": string(token)})
	}
}
