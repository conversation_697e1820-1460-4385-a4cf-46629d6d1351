package tests

import (
	"encoding/json"
	"strings"
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

func TestErrorHandling(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Invalid JSON Request Body", func(t *testing.T) {
		// Test with malformed JSON
		req := `{"name": "test", "invalid": json}`
		
		resp := client.Request("POST", "/login", nil, map[string]string{
			"Content-Type": "application/json",
		})
		
		// The request should fail due to invalid JSON
		if resp.Code == 200 {
			t.Error("Expected request to fail with invalid JSON")
		}
	})

	t.Run("Missing Required Fields", func(t *testing.T) {
		// Test login with missing fields
		tests := []struct {
			name    string
			request map[string]interface{}
		}{
			{
				name:    "Missing clinic name",
				request: map[string]interface{}{"username": "test", "password": "test"},
			},
			{
				name:    "Missing username",
				request: map[string]interface{}{"clinicName": "test", "password": "test"},
			},
			{
				name:    "Missing password",
				request: map[string]interface{}{"clinicName": "test", "username": "test"},
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				resp := client.Request("POST", "/login", tt.request, nil)
				if resp.Code != 400 {
					t.Errorf("Expected 400 for %s, got %d", tt.name, resp.Code)
				}
			})
		}
	})

	t.Run("Resource Not Found", func(t *testing.T) {
		clinic, user, _ := testData.CreateTestClinic("notfoundtest")
		token := client.GetAuthToken(clinic.Name, user.Username, "password")

		// Try to access non-existent user
		resp := client.RequestWithAuth("PATCH", "/users/non-existent-id", map[string]interface{}{
			"name": "Updated Name",
		}, token)

		if resp.Code != 404 {
			t.Errorf("Expected 404 for non-existent user, got %d", resp.Code)
		}

		// Try to access non-existent patient
		resp = client.RequestWithAuth("GET", "/patients/non-existent-id", nil, token)
		if resp.Code != 404 {
			t.Errorf("Expected 404 for non-existent patient, got %d", resp.Code)
		}
	})

	t.Run("Unauthorized Access", func(t *testing.T) {
		clinic1, user1, _ := testData.CreateTestClinic("clinic1")
		clinic2, user2, _ := testData.CreateTestClinic("clinic2")
		
		token1 := client.GetAuthToken(clinic1.Name, user1.Username, "password")
		token2 := client.GetAuthToken(clinic2.Name, user2.Username, "password")

		// Create patient in clinic1
		patient := testData.CreateTestPatient(clinic1.ID, "Test Patient", "01234567890")

		// Try to access clinic1's patient with clinic2's token
		resp := client.RequestWithAuth("GET", "/patients/"+patient.ID, nil, token2)
		if resp.Code != 404 { // Should return 404 due to clinic isolation
			t.Errorf("Expected 404 for cross-clinic access, got %d", resp.Code)
		}

		// Try to update clinic1's patient with clinic2's token
		resp = client.RequestWithAuth("PATCH", "/patients/"+patient.ID, map[string]interface{}{
			"name": "Hacked Name",
		}, token2)
		if resp.Code != 404 {
			t.Errorf("Expected 404 for cross-clinic update, got %d", resp.Code)
		}
	})

	t.Run("Rate Limiting and Large Requests", func(t *testing.T) {
		clinic, user, _ := testData.CreateTestClinic("ratelimittest")
		token := client.GetAuthToken(clinic.Name, user.Username, "password")

		// Test with very large request body
		largeData := strings.Repeat("a", 10000) // 10KB string
		resp := client.RequestWithAuth("PATCH", "/users/me", map[string]interface{}{
			"name": largeData,
		}, token)

		// Should handle large requests gracefully
		if resp.Code == 500 {
			t.Error("Server should handle large requests gracefully")
		}
	})

	t.Run("Concurrent Access", func(t *testing.T) {
		clinic, user, _ := testData.CreateTestClinic("concurrenttest")
		token := client.GetAuthToken(clinic.Name, user.Username, "password")

		// Create a patient
		patient := testData.CreateTestPatient(clinic.ID, "Concurrent Patient", "01234567890")

		// Simulate concurrent updates
		done := make(chan bool, 2)
		
		go func() {
			resp := client.RequestWithAuth("PATCH", "/patients/"+patient.ID, map[string]interface{}{
				"name": "Updated by Goroutine 1",
			}, token)
			if resp.Code != 200 {
				t.Errorf("Concurrent update 1 failed with status %d", resp.Code)
			}
			done <- true
		}()

		go func() {
			resp := client.RequestWithAuth("PATCH", "/patients/"+patient.ID, map[string]interface{}{
				"address": "Updated by Goroutine 2",
			}, token)
			if resp.Code != 200 {
				t.Errorf("Concurrent update 2 failed with status %d", resp.Code)
			}
			done <- true
		}()

		// Wait for both goroutines to complete
		<-done
		<-done

		// Verify patient still exists and has valid data
		var updatedPatient lib.DentalClinicPatient
		if err := server.Db.First(&updatedPatient, patient.ID).Error; err != nil {
			t.Fatalf("Failed to fetch patient after concurrent updates: %v", err)
		}
	})

	t.Run("SQL Injection Prevention", func(t *testing.T) {
		// Test SQL injection in login
		maliciousLogin := LoginRequest{
			ClinicName: "test'; DROP TABLE dental_clinics; --",
			Username:   "admin",
			Password:   "password",
		}

		resp := client.Request("POST", "/login", maliciousLogin, nil)
		// Should not cause server error (500), should return 404 or 401
		if resp.Code == 500 {
			t.Error("SQL injection attempt caused server error")
		}

		// Verify tables still exist by creating a test clinic
		_, _, _ = testData.CreateTestClinic("sqlinjectiontest")
	})

	t.Run("Input Validation", func(t *testing.T) {
		clinic, user, _ := testData.CreateTestClinic("validationtest")
		token := client.GetAuthToken(clinic.Name, user.Username, "password")

		// Test invalid email format
		resp := client.RequestWithAuth("POST", "/patients", map[string]interface{}{
			"name":        "Test Patient",
			"email":       "invalid-email",
			"phoneNumber": "01234567890",
		}, token)

		// Should validate email format
		if resp.Code == 201 {
			t.Error("Should validate email format")
		}

		// Test invalid phone number format
		resp = client.RequestWithAuth("POST", "/patients", map[string]interface{}{
			"name":        "Test Patient",
			"phoneNumber": "invalid-phone",
		}, token)

		// Should handle invalid phone gracefully
		if resp.Code == 500 {
			t.Error("Should handle invalid phone number gracefully")
		}

		// Test extremely long input
		longName := strings.Repeat("a", 1000)
		resp = client.RequestWithAuth("POST", "/patients", map[string]interface{}{
			"name":        longName,
			"phoneNumber": "01234567890",
		}, token)

		// Should handle long input gracefully
		if resp.Code == 500 {
			t.Error("Should handle long input gracefully")
		}
	})

	t.Run("Content Type Validation", func(t *testing.T) {
		// Test with wrong content type
		resp := client.Request("POST", "/login", map[string]interface{}{
			"clinicName": "test",
			"username":   "test",
			"password":   "test",
		}, map[string]string{
			"Content-Type": "text/plain",
		})

		// Should handle wrong content type gracefully
		if resp.Code == 500 {
			t.Error("Should handle wrong content type gracefully")
		}
	})

	t.Run("Database Connection Errors", func(t *testing.T) {
		// This test would require mocking database failures
		// For now, we'll test that the application handles database queries gracefully
		
		clinic, user, _ := testData.CreateTestClinic("dbconntest")
		token := client.GetAuthToken(clinic.Name, user.Username, "password")

		// Make a request that requires database access
		resp := client.RequestWithAuth("GET", "/users/me", nil, token)
		if resp.Code != 200 {
			t.Errorf("Database-dependent request failed: %d", resp.Code)
		}
	})

	t.Run("Response Format Consistency", func(t *testing.T) {
		clinic, user, _ := testData.CreateTestClinic("responsetest")
		token := client.GetAuthToken(clinic.Name, user.Username, "password")

		// Test successful response format
		resp := client.RequestWithAuth("GET", "/users/me", nil, token)
		if resp.Code == 200 {
			var result map[string]interface{}
			if err := json.Unmarshal(resp.Body.Bytes(), &result); err != nil {
				t.Errorf("Successful response should be valid JSON: %v", err)
			}
		}

		// Test error response format
		resp = client.Request("POST", "/login", map[string]interface{}{
			"clinicName": "nonexistent",
			"username":   "test",
			"password":   "test",
		}, nil)

		if resp.Code >= 400 {
			var result map[string]interface{}
			if err := json.Unmarshal(resp.Body.Bytes(), &result); err != nil {
				t.Errorf("Error response should be valid JSON: %v", err)
			}

			// Error responses should have an error field
			if _, exists := result["error"]; !exists {
				t.Error("Error response should contain 'error' field")
			}
		}
	})
}
