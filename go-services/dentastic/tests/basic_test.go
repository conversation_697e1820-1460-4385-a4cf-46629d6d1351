package tests

import (
	"testing"

	"gitlab.com/payrows/dentastic/lib"
	"golang.org/x/crypto/bcrypt"
)

func TestBasicDatabaseOperations(t *testing.T) {
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Create Subscription Plan", func(t *testing.T) {
		plan := &lib.DentalClinicSubscriptionPlan{
			Name:        "Basic Test Plan",
			Price:       50.0,
			Currency:    "USD",
			Active:      true,
			CanAddUsers: true,
			CanSendSMSs: true,
		}

		if err := testData.db.Create(plan).Error; err != nil {
			t.Fatalf("Failed to create subscription plan: %v", err)
		}

		if plan.ID == "" {
			t.Error("Plan ID should not be empty after creation")
		}

		// Verify plan was created
		var fetchedPlan lib.DentalClinicSubscriptionPlan
		if err := testData.db.Where("id = ?", plan.ID).First(&fetchedPlan).Error; err != nil {
			t.Fatalf("Failed to fetch created plan: %v", err)
		}

		if fetchedPlan.Name != "Basic Test Plan" {
			t.Errorf("Expected plan name 'Basic Test Plan', got '%s'", fetchedPlan.Name)
		}
	})

	t.Run("Create Test Data with Factory", func(t *testing.T) {
		clinic, user, merchant := testData.CreateTestClinic("factorytest")

		// Verify all entities have IDs
		if clinic.ID == "" {
			t.Error("Clinic ID should not be empty")
		}
		if user.ID == "" {
			t.Error("User ID should not be empty")
		}
		if merchant.ID == "" {
			t.Error("Merchant ID should not be empty")
		}

		// Verify relationships
		if user.ClinicID != clinic.ID {
			t.Errorf("User clinic ID %s should match clinic ID %s", user.ClinicID, clinic.ID)
		}
		if clinic.MerchantID != merchant.ID {
			t.Errorf("Clinic merchant ID %s should match merchant ID %s", clinic.MerchantID, merchant.ID)
		}

		// Verify user password is properly hashed
		if user.Password == "" {
			t.Error("User password should not be empty")
		}

		// Test password verification
		err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte("password"))
		if err != nil {
			t.Errorf("Password verification failed: %v", err)
		}
	})

	t.Run("Create Patient", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("patienttest")
		patient := testData.CreateTestPatient(clinic.ID, "Test Patient", "01234567890")

		if patient.ID == "" {
			t.Error("Patient ID should not be empty")
		}
		if patient.ClinicID != clinic.ID {
			t.Errorf("Patient clinic ID %s should match clinic ID %s", patient.ClinicID, clinic.ID)
		}
		if patient.Name != "Test Patient" {
			t.Errorf("Expected patient name 'Test Patient', got '%s'", patient.Name)
		}
	})

	t.Run("Create Additional User", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("additionalusertest")
		additionalUser := testData.CreateTestUser(clinic.ID, "Additional User", "adduser", lib.DentalClinicRoleAdmin)

		if additionalUser.ID == "" {
			t.Error("Additional user ID should not be empty")
		}
		if additionalUser.ClinicID != clinic.ID {
			t.Errorf("Additional user clinic ID %s should match clinic ID %s", additionalUser.ClinicID, clinic.ID)
		}
		if additionalUser.Role != lib.DentalClinicRoleAdmin {
			t.Errorf("Expected role 'admin', got '%s'", additionalUser.Role)
		}
	})
}

func TestPasswordHashing(t *testing.T) {
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Password Hashing and Verification", func(t *testing.T) {
		plainPassword := "testpassword123"

		// Hash password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(plainPassword), 10)
		if err != nil {
			t.Fatalf("Failed to hash password: %v", err)
		}

		// Verify password
		err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(plainPassword))
		if err != nil {
			t.Errorf("Password verification failed: %v", err)
		}

		// Verify wrong password fails
		err = bcrypt.CompareHashAndPassword(hashedPassword, []byte("wrongpassword"))
		if err == nil {
			t.Error("Wrong password should not verify")
		}
	})

	t.Run("Test User Password", func(t *testing.T) {
		_, user, _ := testData.CreateTestClinic("passwordtest")

		// The test factory uses "password" as the test password
		err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte("password"))
		if err != nil {
			t.Errorf("Test user password verification failed: %v", err)
		}

		// Update user password
		newPassword := "newpassword123"
		hashedNewPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), 10)
		if err != nil {
			t.Fatalf("Failed to hash new password: %v", err)
		}

		user.Password = string(hashedNewPassword)
		if err := testData.db.Save(user).Error; err != nil {
			t.Fatalf("Failed to update user password: %v", err)
		}

		// Verify new password
		var updatedUser lib.DentalClinicUser
		if err := testData.db.Where("id = ?", user.ID).First(&updatedUser).Error; err != nil {
			t.Fatalf("Failed to fetch updated user: %v", err)
		}

		err = bcrypt.CompareHashAndPassword([]byte(updatedUser.Password), []byte(newPassword))
		if err != nil {
			t.Errorf("New password verification failed: %v", err)
		}

		// Old password should not work
		err = bcrypt.CompareHashAndPassword([]byte(updatedUser.Password), []byte("password"))
		if err == nil {
			t.Error("Old password should not work after update")
		}
	})
}

func TestDatabaseConstraints(t *testing.T) {
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Unique Constraints", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("uniquetest")

		// Try to create another clinic with the same name
		duplicateClinic := &lib.DentalClinic{
			DisplayName: "Duplicate Clinic",
			Name:        clinic.Name, // Same name should cause conflict
			Currency:    "USD",
			Country:     "USA",
		}
		duplicateClinic.MerchantID = clinic.MerchantID

		err := testData.db.Create(duplicateClinic).Error
		// SQLite may or may not enforce this constraint depending on configuration
		// For now, we'll just verify the operation completes without panic
		if err != nil {
			t.Logf("Duplicate clinic creation failed as expected: %v", err)
		} else {
			t.Logf("Duplicate clinic creation succeeded (SQLite may not enforce unique constraints)")
		}
	})

	t.Run("Foreign Key Relationships", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("fktest")

		// Create patient with valid clinic ID
		patient := &lib.DentalClinicPatient{
			Name:        "Valid Patient",
			PhoneNumber: "01234567890",
			ClinicID:    clinic.ID,
		}

		if err := testData.db.Create(patient).Error; err != nil {
			t.Fatalf("Failed to create patient with valid clinic ID: %v", err)
		}

		// Try to create patient with invalid clinic ID
		invalidPatient := &lib.DentalClinicPatient{
			Name:        "Invalid Patient",
			PhoneNumber: "01987654321",
			ClinicID:    "invalid-clinic-id",
		}

		err := testData.db.Create(invalidPatient).Error
		// SQLite may not enforce foreign key constraints by default
		if err != nil {
			t.Logf("Foreign key constraint enforced as expected: %v", err)
		} else {
			t.Logf("Foreign key constraint not enforced (SQLite default behavior)")
		}
	})
}

func TestDataCleanup(t *testing.T) {
	testData := NewTestData(t)

	t.Run("Create and Cleanup Data", func(t *testing.T) {
		// Create test data
		clinic, user, _ := testData.CreateTestClinic("cleanuptest")
		patient := testData.CreateTestPatient(clinic.ID, "Cleanup Patient", "01234567890")

		// Verify data exists
		var clinicCount, userCount, patientCount int64
		testData.db.Model(&lib.DentalClinic{}).Where("id = ?", clinic.ID).Count(&clinicCount)
		testData.db.Model(&lib.DentalClinicUser{}).Where("id = ?", user.ID).Count(&userCount)
		testData.db.Model(&lib.DentalClinicPatient{}).Where("id = ?", patient.ID).Count(&patientCount)

		if clinicCount != 1 {
			t.Errorf("Expected 1 clinic, found %d", clinicCount)
		}
		if userCount != 1 {
			t.Errorf("Expected 1 user, found %d", userCount)
		}
		if patientCount != 1 {
			t.Errorf("Expected 1 patient, found %d", patientCount)
		}

		// Cleanup
		testData.CleanupTestData()

		// Verify data is cleaned up
		testData.db.Model(&lib.DentalClinic{}).Where("id = ?", clinic.ID).Count(&clinicCount)
		testData.db.Model(&lib.DentalClinicUser{}).Where("id = ?", user.ID).Count(&userCount)
		testData.db.Model(&lib.DentalClinicPatient{}).Where("id = ?", patient.ID).Count(&patientCount)

		if clinicCount != 0 {
			t.Errorf("Expected 0 clinics after cleanup, found %d", clinicCount)
		}
		if userCount != 0 {
			t.Errorf("Expected 0 users after cleanup, found %d", userCount)
		}
		if patientCount != 0 {
			t.Errorf("Expected 0 patients after cleanup, found %d", patientCount)
		}
	})
}
