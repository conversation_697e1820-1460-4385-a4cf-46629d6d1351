package tests

import (
	"encoding/json"
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

func TestMerchantClinicOperations(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Create test clinic and user
	clinic, user, _ := testData.CreateTestClinic("merchanttest")
	token := client.GetAuthToken(clinic.Name, user.Username, "password")

	t.Run("Get User Profile", func(t *testing.T) {
		resp := client.RequestWithAuth("GET", "/users/me", nil, token)
		if resp.Code != 200 {
			t.Fatalf("Get user profile failed with status %d: %s", resp.Code, resp.Body.String())
		}

		var result map[string]interface{}
		if err := json.Unmarshal(resp.Body.Bytes(), &result); err != nil {
			t.Fatalf("Failed to parse user profile response: %v", err)
		}

		// Verify user data
		userResult, ok := result["user"].(map[string]interface{})
		if !ok {
			t.Fatal("User data not found in response")
		}

		if userResult["username"] != user.Username {
			t.Errorf("Expected username %s, got %s", user.Username, userResult["username"])
		}

		if userResult["name"] != user.Name {
			t.Errorf("Expected name %s, got %s", user.Name, userResult["name"])
		}

		if userResult["role"] != string(user.Role) {
			t.Errorf("Expected role %s, got %s", string(user.Role), userResult["role"])
		}
	})

	t.Run("Update Clinic Information", func(t *testing.T) {
		updateReq := map[string]interface{}{
			"displayName": "Updated Clinic Name",
		}

		resp := client.RequestWithAuth("PATCH", "/clinics", updateReq, token)
		if resp.Code != 200 {
			t.Fatalf("Update clinic failed with status %d: %s", resp.Code, resp.Body.String())
		}

		// Verify the update was applied
		var updatedClinic lib.DentalClinic
		if err := server.Db.First(&updatedClinic, clinic.ID).Error; err != nil {
			t.Fatalf("Failed to fetch updated clinic: %v", err)
		}

		if updatedClinic.DisplayName != "Updated Clinic Name" {
			t.Errorf("Expected display name 'Updated Clinic Name', got '%s'", updatedClinic.DisplayName)
		}
	})

	t.Run("Update Patient Notification Config", func(t *testing.T) {
		updateReq := map[string]interface{}{
			"patientNotificationConfig": map[string]interface{}{
				"language":        "en",
				"daysPrior":       2,
				"includeLocation": true,
			},
		}

		resp := client.RequestWithAuth("PATCH", "/clinics", updateReq, token)
		if resp.Code != 200 {
			t.Fatalf("Update notification config failed with status %d: %s", resp.Code, resp.Body.String())
		}

		// Verify notification config was created/updated
		var config lib.DentalClinicPatientNotificationConfig
		if err := server.Db.Where("clinic_id = ?", clinic.ID).First(&config).Error; err != nil {
			t.Fatalf("Failed to fetch notification config: %v", err)
		}

		if config.Language != lib.DentalClinicSupportedNotificationLanguageEn {
			t.Errorf("Expected language 'en', got '%s'", config.Language)
		}

		if config.DaysPrior != 2 {
			t.Errorf("Expected days prior 2, got %d", config.DaysPrior)
		}

		if !config.IncludeLocation {
			t.Error("Expected include location to be true")
		}
	})
}

func TestUserManagement(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Create test clinic and master user
	clinic, masterUser, _ := testData.CreateTestClinic("usermgmt")
	masterToken := client.GetAuthToken(clinic.Name, masterUser.Username, "password")

	t.Run("Create New User", func(t *testing.T) {
		createUserReq := map[string]interface{}{
			"name":        "Dr. New User",
			"username":    "newuser",
			"password":    "password123",
			"phoneNumber": "01987654321",
			"role":        "admin",
			"isDentist":   true,
			"percentage":  25.5,
			"hourlyRate":  150.0,
		}

		resp := client.RequestWithAuth("POST", "/users", createUserReq, masterToken)
		if resp.Code != 201 {
			t.Fatalf("Create user failed with status %d: %s", resp.Code, resp.Body.String())
		}

		var result map[string]interface{}
		if err := json.Unmarshal(resp.Body.Bytes(), &result); err != nil {
			t.Fatalf("Failed to parse create user response: %v", err)
		}

		userResult, ok := result["user"].(map[string]interface{})
		if !ok {
			t.Fatal("User data not found in response")
		}

		if userResult["username"] != "newuser" {
			t.Errorf("Expected username 'newuser', got %s", userResult["username"])
		}

		if userResult["role"] != "admin" {
			t.Errorf("Expected role 'admin', got %s", userResult["role"])
		}
	})

	t.Run("List Users", func(t *testing.T) {
		resp := client.RequestWithAuth("GET", "/users", nil, masterToken)
		if resp.Code != 200 {
			t.Fatalf("List users failed with status %d: %s", resp.Code, resp.Body.String())
		}

		var result map[string]interface{}
		if err := json.Unmarshal(resp.Body.Bytes(), &result); err != nil {
			t.Fatalf("Failed to parse list users response: %v", err)
		}

		users, ok := result["users"].([]interface{})
		if !ok {
			t.Fatal("Users array not found in response")
		}

		// Should have at least the master user and the newly created user
		if len(users) < 2 {
			t.Errorf("Expected at least 2 users, got %d", len(users))
		}
	})

	t.Run("Update User Profile", func(t *testing.T) {
		updateReq := map[string]interface{}{
			"name":        "Updated Name",
			"phoneNumber": "01111111111",
		}

		resp := client.RequestWithAuth("PATCH", "/users/me", updateReq, masterToken)
		if resp.Code != 200 {
			t.Fatalf("Update user profile failed with status %d: %s", resp.Code, resp.Body.String())
		}

		// Verify the update
		var updatedUser lib.DentalClinicUser
		if err := server.Db.First(&updatedUser, masterUser.ID).Error; err != nil {
			t.Fatalf("Failed to fetch updated user: %v", err)
		}

		if updatedUser.Name != "Updated Name" {
			t.Errorf("Expected name 'Updated Name', got '%s'", updatedUser.Name)
		}

		if updatedUser.PhoneNumber != "01111111111" {
			t.Errorf("Expected phone '01111111111', got '%s'", updatedUser.PhoneNumber)
		}
	})
}

func TestRoleBasedAccess(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Create test clinic and users with different roles
	clinic, masterUser, _ := testData.CreateTestClinic("roletest")
	adminUser := testData.CreateTestUser(clinic.ID, "Admin User", "adminuser", lib.DentalClinicRoleAdmin)
	basicUser := testData.CreateTestUser(clinic.ID, "Basic User", "basicuser", lib.DentalClinicRoleBasic)

	masterToken := client.GetAuthToken(clinic.Name, masterUser.Username, "password")
	adminToken := client.GetAuthToken(clinic.Name, adminUser.Username, "password")
	basicToken := client.GetAuthToken(clinic.Name, basicUser.Username, "password")

	t.Run("Master User Access", func(t *testing.T) {
		// Master should be able to create users
		createUserReq := map[string]interface{}{
			"name":     "Test User",
			"username": "testuser",
			"password": "password123",
			"role":     "secretary",
		}

		resp := client.RequestWithAuth("POST", "/users", createUserReq, masterToken)
		if resp.Code != 201 {
			t.Errorf("Master user should be able to create users, got status %d", resp.Code)
		}

		// Master should be able to update clinic
		updateReq := map[string]interface{}{
			"displayName": "Master Updated Clinic",
		}

		resp = client.RequestWithAuth("PATCH", "/clinics", updateReq, masterToken)
		if resp.Code != 200 {
			t.Errorf("Master user should be able to update clinic, got status %d", resp.Code)
		}
	})

	t.Run("Admin User Access", func(t *testing.T) {
		// Admin should be able to create users
		createUserReq := map[string]interface{}{
			"name":     "Admin Created User",
			"username": "admincreated",
			"password": "password123",
			"role":     "secretary",
		}

		resp := client.RequestWithAuth("POST", "/users", createUserReq, adminToken)
		if resp.Code != 201 {
			t.Errorf("Admin user should be able to create users, got status %d", resp.Code)
		}

		// Admin should NOT be able to update clinic (master only)
		updateReq := map[string]interface{}{
			"displayName": "Admin Updated Clinic",
		}

		resp = client.RequestWithAuth("PATCH", "/clinics", updateReq, adminToken)
		if resp.Code != 401 {
			t.Errorf("Admin user should NOT be able to update clinic, got status %d", resp.Code)
		}
	})

	t.Run("Basic User Access", func(t *testing.T) {
		// Basic user should NOT be able to create users
		createUserReq := map[string]interface{}{
			"name":     "Basic Created User",
			"username": "basiccreated",
			"password": "password123",
			"role":     "secretary",
		}

		resp := client.RequestWithAuth("POST", "/users", createUserReq, basicToken)
		if resp.Code != 401 {
			t.Errorf("Basic user should NOT be able to create users, got status %d", resp.Code)
		}

		// Basic user should NOT be able to list users
		resp = client.RequestWithAuth("GET", "/users", nil, basicToken)
		if resp.Code != 401 {
			t.Errorf("Basic user should NOT be able to list users, got status %d", resp.Code)
		}

		// Basic user should be able to get their own profile
		resp = client.RequestWithAuth("GET", "/users/me", nil, basicToken)
		if resp.Code != 200 {
			t.Errorf("Basic user should be able to get own profile, got status %d", resp.Code)
		}
	})
}

func TestSubscriptionValidation(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// Create test clinic with expired subscription
	clinic, user, _ := testData.CreateTestClinic("expiredtest")
	
	// Set subscription to expired
	clinic.SubscriptionEndDate = "2020-01-01T00:00:00Z"
	clinic.SubscriptionRenewedAt = "2020-01-01T00:00:00Z"
	if err := server.Db.Save(clinic).Error; err != nil {
		t.Fatalf("Failed to update clinic subscription: %v", err)
	}

	token := client.GetAuthToken(clinic.Name, user.Username, "password")

	t.Run("Expired Subscription Blocks Write Operations", func(t *testing.T) {
		// GET requests should work
		resp := client.RequestWithAuth("GET", "/users/me", nil, token)
		if resp.Code != 200 {
			t.Errorf("GET requests should work with expired subscription, got status %d", resp.Code)
		}

		// POST requests should be blocked
		createUserReq := map[string]interface{}{
			"name":     "Test User",
			"username": "testuser",
			"password": "password123",
			"role":     "secretary",
		}

		resp = client.RequestWithAuth("POST", "/users", createUserReq, token)
		if resp.Code != 412 {
			t.Errorf("POST requests should be blocked with expired subscription, got status %d", resp.Code)
		}

		// PATCH requests should be blocked
		updateReq := map[string]interface{}{
			"displayName": "Updated Name",
		}

		resp = client.RequestWithAuth("PATCH", "/clinics", updateReq, token)
		if resp.Code != 412 {
			t.Errorf("PATCH requests should be blocked with expired subscription, got status %d", resp.Code)
		}
	})
}
