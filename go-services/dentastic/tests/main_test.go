package tests

import (
	"log"
	"os"
	"testing"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/helpers"
	"gitlab.com/payrows/dentastic/lib"
)

var app *fiber.App
var server *config.Server

func setup() {
	// dbUrl := helpers.EnvVarOrDefault("DB_URL", "postgresql://postgres:password@localhost:5432/payrows_dentastic_test")
	dbUrl := helpers.EnvVarOrDefault("DB_URL", "sqlite://payrows_dentastic_test")
	os.Setenv("DB_URL", dbUrl)
	os.Setenv("PORT", "3010")
	app, server = lib.Initialize(lib.IntializationConfig{
		ShouldMigrate:    true,
		MigrateMerchant:  true,
		ExitAfterMigrate: false,
	})

	plan := lib.DentalClinicSubscriptionPlan{
		Name:        "Test Plan",
		Price:       100,
		Currency:    "EGP",
		Active:      true,
		CanAddUsers: true,
		CanSendSMSs: true,
	}
	if err := server.Db.Create(&plan).Error; err != nil {
		log.Fatalf("Creating plan failed: %v\n", err)
	}
}

func shutdown() {
	log.Println("Shutting down")
	server.Db.Exec("DROP SCHEMA public CASCADE; CREATE SCHEMA public;")
}

func TestMain(m *testing.M) {
	setup()
	code := m.Run()
	shutdown()
	os.Exit(code)
}
