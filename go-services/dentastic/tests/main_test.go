package tests

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/dentastic/lib"
)

var app *fiber.App
var server *config.Server
var testDbPath string

func setup() {
	// Create a unique test database file for each test run
	timestamp := time.Now().Format("20060102_150405")
	testDbPath = filepath.Join(os.TempDir(), fmt.Sprintf("payrows_dentastic_test_%s.db", timestamp))

	dbUrl := fmt.Sprintf("sqlite://%s", testDbPath)
	os.Setenv("DB_URL", dbUrl)
	os.Setenv("PORT", "3010")
	os.Setenv("JWT_SECRET", "test_jwt_secret_for_integration_tests")

	// Disable external services for testing
	os.Setenv("S3_KEY", "test_key")
	os.Setenv("S3_SECRET", "test_secret")
	os.Setenv("STRIPE_KEY", "sk_test_51MNzKbGdNq0t4eg1DwHWuT3umVLhJS1JYyDhGLkZrB5qV6sevl3naZ7hfnuFLGrBp9TUSBUttcSKw7I7eYOaLiO800Z76yeZL6")

	// Disable external service calls for testing
	os.Setenv("DISABLE_STRIPE", "true")
	os.Setenv("DISABLE_S3", "true")
	os.Setenv("DISABLE_EMAIL", "true")
	os.Setenv("DISABLE_SMS", "true")

	log.Printf("Setting up test database at: %s", testDbPath)

	app, server = lib.Initialize(lib.IntializationConfig{
		ShouldMigrate:    true,
		MigrateMerchant:  true,
		ExitAfterMigrate: false,
	})

	// Create test subscription plan
	plan := lib.DentalClinicSubscriptionPlan{
		Name:        "Test Plan",
		Price:       100,
		Currency:    "EGP",
		Active:      true,
		CanAddUsers: true,
		CanSendSMSs: true,
	}
	if err := server.Db.Create(&plan).Error; err != nil {
		log.Fatalf("Creating test plan failed: %v\n", err)
	}

	log.Println("Test setup completed successfully")
}

func shutdown() {
	log.Println("Shutting down test environment")

	// Close database connection
	if server != nil && server.Db != nil {
		sqlDB, err := server.Db.DB()
		if err == nil {
			sqlDB.Close()
		}
	}

	// Clean up test database file
	if testDbPath != "" {
		if err := os.Remove(testDbPath); err != nil {
			log.Printf("Warning: Failed to remove test database file %s: %v", testDbPath, err)
		} else {
			log.Printf("Cleaned up test database file: %s", testDbPath)
		}
	}
}

func TestMain(m *testing.M) {
	setup()
	code := m.Run()
	shutdown()
	os.Exit(code)
}
