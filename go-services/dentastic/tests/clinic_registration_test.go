package tests

import (
	"fmt"
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

func TestClinicRegistration(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Successful Clinic Registration", func(t *testing.T) {
		// Get subscription plan for clinic creation
		plan := new(lib.DentalClinicSubscriptionPlan)
		if err := testData.db.First(plan).Error; err != nil {
			t.Fatalf("Failed to get subscription plan: %v", err)
		}

		// Test clinic registration
		clinicReq := ClinicCreateRequest{
			DisplayName:      "Test Dental Clinic",
			Username:         "testadmin",
			Name:             "Dr. Test Admin",
			Email:            "<EMAIL>",
			Password:         "password123",
			PhoneNumber:      "01234567890",
			SystemName:       "testclinic",
			SubscriptionPlan: plan.ID,
		}

		resp := client.Request("POST", "/clinics", clinicReq, nil)
		if resp.Code != 201 {
			t.Fatalf("Clinic creation failed with status %d: %s", resp.Code, resp.Body.String())
		}

		var createResult map[string]interface{}
		result := client.AssertJSONResponse(resp, map[string]interface{}{})
		createResult = result

		// Verify token is returned
		token, ok := createResult["token"].(string)
		if !ok || token == "" {
			t.Fatal("Token not returned in clinic creation response")
		}

		// Verify clinic was created in database
		var clinic lib.DentalClinic
		if err := testData.db.Where("name = ?", "testclinic").First(&clinic).Error; err != nil {
			t.Fatalf("Failed to find created clinic: %v", err)
		}

		if clinic.DisplayName != "Test Dental Clinic" {
			t.Errorf("Expected display name 'Test Dental Clinic', got '%s'", clinic.DisplayName)
		}

		// Verify user was created
		var user lib.DentalClinicUser
		if err := testData.db.Where("clinic_id = ? AND username = ?", clinic.ID, "testadmin").First(&user).Error; err != nil {
			t.Fatalf("Failed to find created user: %v", err)
		}

		if user.Name != "Dr. Test Admin" {
			t.Errorf("Expected user name 'Dr. Test Admin', got '%s'", user.Name)
		}

		if user.Role != lib.DentalClinicRoleMaster {
			t.Errorf("Expected user role 'master', got '%s'", user.Role)
		}

		// Test login with created credentials
		loginReq := LoginRequest{
			ClinicName: "testclinic",
			Username:   "testadmin",
			Password:   "password123",
		}

		loginResp := client.Request("POST", "/login", loginReq, nil)
		if loginResp.Code != 200 {
			t.Errorf("Login failed after registration with status %d: %s", loginResp.Code, loginResp.Body.String())
		}
	})

	t.Run("Registration Validation", func(t *testing.T) {
		// Get subscription plan
		plan := new(lib.DentalClinicSubscriptionPlan)
		if err := testData.db.First(plan).Error; err != nil {
			t.Fatalf("Failed to get subscription plan: %v", err)
		}

		tests := []struct {
			name           string
			request        ClinicCreateRequest
			expectedStatus int
			description    string
		}{
			{
				name: "Valid Registration",
				request: ClinicCreateRequest{
					DisplayName:      "Valid Clinic",
					Username:         "validuser",
					Name:             "Dr. Valid User",
					Email:            "<EMAIL>",
					Password:         "password123",
					PhoneNumber:      "01234567891", // Different phone number
					SystemName:       "validclinic",
					SubscriptionPlan: plan.ID,
				},
				expectedStatus: 201,
				description:    "Should succeed with valid data",
			},
			{
				name: "Missing Display Name",
				request: ClinicCreateRequest{
					Username:         "testuser",
					Name:             "Dr. Test",
					Email:            "<EMAIL>",
					Password:         "password123",
					PhoneNumber:      "01234567890",
					SystemName:       "testclinic2",
					SubscriptionPlan: plan.ID,
				},
				expectedStatus: 400,
				description:    "Should fail with missing display name",
			},
			{
				name: "Missing Email",
				request: ClinicCreateRequest{
					DisplayName:      "Test Clinic",
					Username:         "testuser",
					Name:             "Dr. Test",
					Password:         "password123",
					PhoneNumber:      "01234567890",
					SystemName:       "testclinic3",
					SubscriptionPlan: plan.ID,
				},
				expectedStatus: 400,
				description:    "Should fail with missing email",
			},
			{
				name: "Invalid Email Format",
				request: ClinicCreateRequest{
					DisplayName:      "Test Clinic",
					Username:         "testuser",
					Name:             "Dr. Test",
					Email:            "invalid-email",
					Password:         "password123",
					PhoneNumber:      "01234567892", // Different phone number
					SystemName:       "testclinic4",
					SubscriptionPlan: plan.ID,
				},
				expectedStatus: 400,
				description:    "Should fail with invalid email format",
			},
			{
				name: "Short Password",
				request: ClinicCreateRequest{
					DisplayName:      "Test Clinic",
					Username:         "testuser",
					Name:             "Dr. Test",
					Email:            "<EMAIL>",
					Password:         "short",
					PhoneNumber:      "01234567890",
					SystemName:       "testclinic5",
					SubscriptionPlan: plan.ID,
				},
				expectedStatus: 400,
				description:    "Should fail with password too short",
			},
			{
				name: "Missing Phone Number",
				request: ClinicCreateRequest{
					DisplayName:      "Test Clinic",
					Username:         "testuser",
					Name:             "Dr. Test",
					Email:            "<EMAIL>", // Different email
					Password:         "password123",
					SystemName:       "testclinic6",
					SubscriptionPlan: plan.ID,
				},
				expectedStatus: 400,
				description:    "Should fail with missing phone number",
			},
			{
				name: "Missing System Name",
				request: ClinicCreateRequest{
					DisplayName:      "Test Clinic",
					Username:         "testuser",
					Name:             "Dr. Test",
					Email:            "<EMAIL>",
					Password:         "password123",
					PhoneNumber:      "01234567890",
					SubscriptionPlan: plan.ID,
				},
				expectedStatus: 400,
				description:    "Should fail with missing system name",
			},
			{
				name: "Invalid Subscription Plan",
				request: ClinicCreateRequest{
					DisplayName:      "Test Clinic",
					Username:         "testuser",
					Name:             "Dr. Test",
					Email:            "<EMAIL>",
					Password:         "password123",
					PhoneNumber:      "01234567890",
					SystemName:       "testclinic7",
					SubscriptionPlan: "invalid-plan-id",
				},
				expectedStatus: 400,
				description:    "Should fail with invalid subscription plan",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				resp := client.Request("POST", "/clinics", tt.request, nil)
				if resp.Code != tt.expectedStatus {
					t.Errorf("%s: Expected status %d, got %d. Response: %s",
						tt.description, tt.expectedStatus, resp.Code, resp.Body.String())
				}
			})
		}
	})

	t.Run("Duplicate Registration Prevention", func(t *testing.T) {
		// Get subscription plan
		plan := new(lib.DentalClinicSubscriptionPlan)
		if err := testData.db.First(plan).Error; err != nil {
			t.Fatalf("Failed to get subscription plan: %v", err)
		}

		// Create first clinic
		clinicReq := ClinicCreateRequest{
			DisplayName:      "First Clinic",
			Username:         "firstuser",
			Name:             "Dr. First",
			Email:            "<EMAIL>",
			Password:         "password123",
			PhoneNumber:      "01234567893", // Different phone number
			SystemName:       "firstclinic",
			SubscriptionPlan: plan.ID,
		}

		resp := client.Request("POST", "/clinics", clinicReq, nil)
		if resp.Code != 201 {
			t.Fatalf("First clinic creation failed: %s", resp.Body.String())
		}

		// Try to create clinic with same email
		duplicateEmailReq := clinicReq
		duplicateEmailReq.SystemName = "secondclinic"
		duplicateEmailReq.DisplayName = "Second Clinic"
		duplicateEmailReq.Username = "seconduser"

		resp = client.Request("POST", "/clinics", duplicateEmailReq, nil)
		if resp.Code == 201 {
			t.Error("Should not allow duplicate email registration")
		}

		// Try to create clinic with same phone number
		duplicatePhoneReq := clinicReq
		duplicatePhoneReq.Email = "<EMAIL>"
		duplicatePhoneReq.SystemName = "thirdclinic"
		duplicatePhoneReq.DisplayName = "Third Clinic"
		duplicatePhoneReq.Username = "thirduser"
		// Keep the same phone number to test duplicate detection

		resp = client.Request("POST", "/clinics", duplicatePhoneReq, nil)
		if resp.Code == 201 {
			t.Error("Should not allow duplicate phone number registration")
		}

		// Try to create clinic with same system name
		duplicateSystemReq := clinicReq
		duplicateSystemReq.Email = "<EMAIL>"
		duplicateSystemReq.PhoneNumber = "01987654321"
		duplicateSystemReq.DisplayName = "Fourth Clinic"
		duplicateSystemReq.Username = "fourthuser"
		// Keep the same system name to test duplicate detection

		resp = client.Request("POST", "/clinics", duplicateSystemReq, nil)
		if resp.Code == 201 {
			t.Error("Should not allow duplicate system name registration")
		}
	})

	t.Run("Registration with Different Plans", func(t *testing.T) {
		// Create multiple subscription plans
		plans := []lib.DentalClinicSubscriptionPlan{
			{
				Name:        "Basic Plan",
				Price:       50.0,
				Currency:    "USD",
				Active:      true,
				CanAddUsers: false,
				CanSendSMSs: false,
			},
			{
				Name:        "Premium Plan",
				Price:       100.0,
				Currency:    "USD",
				Active:      true,
				CanAddUsers: true,
				CanSendSMSs: true,
			},
		}

		for i, plan := range plans {
			if err := testData.db.Create(&plan).Error; err != nil {
				t.Fatalf("Failed to create plan %d: %v", i, err)
			}
			plans[i] = plan
		}

		// Test registration with each plan
		for i, plan := range plans {
			clinicReq := ClinicCreateRequest{
				DisplayName:      fmt.Sprintf("Clinic %d", i+1),
				Username:         fmt.Sprintf("user%d", i+1),
				Name:             fmt.Sprintf("Dr. User %d", i+1),
				Email:            fmt.Sprintf("<EMAIL>", i+1),
				Password:         "password123",
				PhoneNumber:      fmt.Sprintf("01234567%03d", 894+i), // Unique phone numbers
				SystemName:       fmt.Sprintf("clinic%d", i+1),
				SubscriptionPlan: plan.ID,
			}

			resp := client.Request("POST", "/clinics", clinicReq, nil)
			if resp.Code != 201 {
				t.Errorf("Registration with plan %s failed with status %d: %s",
					plan.Name, resp.Code, resp.Body.String())
			}

			// Verify clinic was created with correct plan
			var clinic lib.DentalClinic
			if err := testData.db.Where("name = ?", clinicReq.SystemName).First(&clinic).Error; err != nil {
				t.Errorf("Failed to find clinic created with plan %s: %v", plan.Name, err)
			} else if clinic.SubscriptionPlanID.String != plan.ID {
				t.Errorf("Clinic created with wrong plan. Expected %s, got %s",
					plan.ID, clinic.SubscriptionPlanID.String)
			}
		}
	})
}
