package tests

import (
	"testing"
)

func TestSimpleAuthentication(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Create Test Clinic and Login", func(t *testing.T) {
		// Create test clinic using our test data factory (bypasses Stripe)
		clinic, user, _ := testData.CreateTestClinic("simpletest")

		// Test login with the created clinic
		loginReq := LoginRequest{
			ClinicName: clinic.Name,
			Username:   user.Username,
			Password:   "password", // This is the test password we use
		}

		resp := client.Request("POST", "/login", loginReq, nil)
		if resp.Code != 200 {
			t.Fatalf("Login failed with status %d: %s", resp.Code, resp.Body.String())
		}

		// Verify response contains token
		result := client.AssertJSONResponse(resp, map[string]interface{}{})
		if _, exists := result["token"]; !exists {
			t.Error("Login response should contain token")
		}
	})

	t.Run("Test Wrong Credentials", func(t *testing.T) {
		// Create test clinic
		clinic, user, _ := testData.CreateTestClinic("wrongcredtest")

		// Test with wrong password
		loginReq := LoginRequest{
			ClinicName: clinic.Name,
			Username:   user.Username,
			Password:   "wrongpassword",
		}

		resp := client.Request("POST", "/login", loginReq, nil)
		if resp.Code != 401 {
			t.Errorf("Expected 401 for wrong password, got %d", resp.Code)
		}

		// Test with wrong username
		loginReq = LoginRequest{
			ClinicName: clinic.Name,
			Username:   "wronguser",
			Password:   "password",
		}

		resp = client.Request("POST", "/login", loginReq, nil)
		if resp.Code != 401 {
			t.Errorf("Expected 401 for wrong username, got %d", resp.Code)
		}

		// Test with wrong clinic name
		loginReq = LoginRequest{
			ClinicName: "wrongclinic",
			Username:   user.Username,
			Password:   "password",
		}

		resp = client.Request("POST", "/login", loginReq, nil)
		if resp.Code != 404 {
			t.Errorf("Expected 404 for wrong clinic name, got %d", resp.Code)
		}
	})

	t.Run("Test Authenticated Request", func(t *testing.T) {
		// Create test clinic
		clinic, user, _ := testData.CreateTestClinic("authtest")

		// Get auth token
		token := client.GetAuthToken(clinic.Name, user.Username, "password")

		// Test authenticated request
		resp := client.RequestWithAuth("GET", "/users/me", nil, token)
		if resp.Code != 200 {
			t.Fatalf("Authenticated request failed with status %d: %s", resp.Code, resp.Body.String())
		}

		// Verify user data in response
		result := client.AssertJSONResponse(resp, map[string]interface{}{})
		userResult, ok := result["user"].(map[string]interface{})
		if !ok {
			t.Fatal("User data not found in response")
		}

		if userResult["username"] != user.Username {
			t.Errorf("Expected username %s, got %s", user.Username, userResult["username"])
		}
	})

	t.Run("Test Unauthenticated Request", func(t *testing.T) {
		// Test request without token
		resp := client.Request("GET", "/users/me", nil, nil)
		if resp.Code != 401 {
			t.Errorf("Expected 401 for unauthenticated request, got %d", resp.Code)
		}

		// Test request with invalid token
		resp = client.RequestWithAuth("GET", "/users/me", nil, "invalid.token.here")
		if resp.Code != 401 {
			t.Errorf("Expected 401 for invalid token, got %d", resp.Code)
		}
	})
}

func TestDatabaseOperations(t *testing.T) {
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Create and Retrieve Clinic", func(t *testing.T) {
		clinic, user, merchant := testData.CreateTestClinic("dbtest")

		// Verify all entities were created with proper IDs
		if clinic.ID == "" {
			t.Error("Clinic ID should not be empty")
		}
		if user.ID == "" {
			t.Error("User ID should not be empty")
		}
		if merchant.ID == "" {
			t.Error("Merchant ID should not be empty")
		}

		// Verify relationships
		if user.ClinicID != clinic.ID {
			t.Errorf("User clinic ID %s should match clinic ID %s", user.ClinicID, clinic.ID)
		}
		if clinic.MerchantID != merchant.ID {
			t.Errorf("Clinic merchant ID %s should match merchant ID %s", clinic.MerchantID, merchant.ID)
		}
	})

	t.Run("Create Patient", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("patienttest")
		patient := testData.CreateTestPatient(clinic.ID, "Test Patient", "01234567890")

		if patient.ID == "" {
			t.Error("Patient ID should not be empty")
		}
		if patient.ClinicID != clinic.ID {
			t.Errorf("Patient clinic ID %s should match clinic ID %s", patient.ClinicID, clinic.ID)
		}
	})

	t.Run("Create Additional User", func(t *testing.T) {
		clinic, _, _ := testData.CreateTestClinic("usertest")
		additionalUser := testData.CreateTestUser(clinic.ID, "Additional User", "adduser", "admin")

		if additionalUser.ID == "" {
			t.Error("Additional user ID should not be empty")
		}
		if additionalUser.ClinicID != clinic.ID {
			t.Errorf("Additional user clinic ID %s should match clinic ID %s", additionalUser.ClinicID, clinic.ID)
		}
	})
}

func TestInputValidation(t *testing.T) {
	client := NewTestClient(t)

	t.Run("Missing Login Fields", func(t *testing.T) {
		tests := []struct {
			name    string
			request LoginRequest
		}{
			{"Missing clinic name", LoginRequest{Username: "test", Password: "test"}},
			{"Missing username", LoginRequest{ClinicName: "test", Password: "test"}},
			{"Missing password", LoginRequest{ClinicName: "test", Username: "test"}},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				resp := client.Request("POST", "/login", tt.request, nil)
				if resp.Code != 400 {
					t.Errorf("Expected 400 for %s, got %d", tt.name, resp.Code)
				}
			})
		}
	})

	t.Run("Empty Login Fields", func(t *testing.T) {
		loginReq := LoginRequest{
			ClinicName: "",
			Username:   "",
			Password:   "",
		}

		resp := client.Request("POST", "/login", loginReq, nil)
		if resp.Code != 400 {
			t.Errorf("Expected 400 for empty fields, got %d", resp.Code)
		}
	})
}
