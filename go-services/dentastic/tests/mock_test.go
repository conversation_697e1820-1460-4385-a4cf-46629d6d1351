package tests

import (
	"database/sql"
	"log"
	"math/rand"
	"time"

	faker "github.com/go-faker/faker/v4"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func insertMock() {
	db, err := gorm.Open(postgres.Open("postgresql://postgres:password@localhost:5432/dentastic_mock"))
	if err != nil {
		log.Fatal(err)
	}
	log.Println("DB Connected")

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("password"), 10)
	if err != nil {
		log.Fatal(err)
	}

	subscriptionPlan := new(lib.DentalClinicSubscriptionPlan)
	subscriptionPlan.Active = true
	subscriptionPlan.Currency = "usd"
	subscriptionPlan.Name = "silver"
	subscriptionPlan.Price = 69
	if err = db.Create(subscriptionPlan).Error; err != nil {
		log.Fatal(err)
	}
	merchantCount := 300
	merchants := make([]models.Merchant, merchantCount)
	for i := 0; i < merchantCount; i++ {
		merchants[i] = models.Merchant{
			DisplayName: faker.FirstName(),
			SystemName:  faker.Username(),
			Email:       faker.Email(),
			PhoneNumber: faker.Phonenumber(),
			Type:        models.MerchantTypeDentalClinic,
		}
	}
	clinics := make([]lib.DentalClinic, merchantCount)
	for i := 0; i < merchantCount; i++ {
		clinics[i] = lib.DentalClinic{
			DisplayName:         merchants[i].DisplayName,
			Name:                faker.Name(),
			SubscriptionPlanID:  sql.NullString{String: subscriptionPlan.ID, Valid: true},
			SubscriptionEndDate: time.Now().Add(time.Duration(rand.Intn(100) * int(time.Hour) * 24)).Format("2006-01-02"),
		}
	}
	var branches []lib.DentalClinicBranch
	var users []lib.DentalClinicUser
	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&merchants).Error; err != nil {
			log.Fatal(err)
		}
		if err := tx.Create(&clinics).Error; err != nil {
			log.Fatal(err)
		}

		cToB := make(map[string][]string)

		for c := 0; c < len(clinics); c++ {
			branchCount := rand.Intn(4) + 1
			cBranchs := make([]lib.DentalClinicBranch, branchCount)
			for i := 0; i < branchCount; i++ {
				cBranchs[i] = lib.DentalClinicBranch{
					ClinicID:  clinics[c].ID,
					Name:      faker.Name(),
					Longitude: sql.NullFloat64{Float64: faker.Longitude(), Valid: true},
					Latitude:  sql.NullFloat64{Float64: faker.Latitude(), Valid: true},
				}
			}
			userCount := rand.Intn(4) + branchCount
			cUsers := make([]lib.DentalClinicUser, userCount)
			for i := 0; i < userCount; i++ {
				role := lib.DentalClinicRoleMaster
				if i > 0 {
					if i < 5 {
						if i%2 != 0 {
							role = lib.DentalClinicRoleSecretary
						} else {
							role = lib.DentalClinicRoleAdmin
						}
					} else {
						role = lib.DentalClinicRoleExternal
					}
				}
				cUsers[i] = lib.DentalClinicUser{
					ClinicID:    clinics[c].ID,
					Name:        faker.Name(),
					Username:    faker.Username(),
					Password:    string(hashedPassword),
					IsDentist:   i%2 == 0,
					PhoneNumber: faker.Phonenumber(),
					Role:        role,
				}
			}
			users = append(users, cUsers...)
			branches = append(branches, cBranchs...)
		}

		if err := tx.Create(&branches).Error; err != nil {
			log.Fatal(err)
		}

		for b := 0; b < len(branches); b++ {
			branch := branches[b]
			bList := cToB[branch.ClinicID]
			bList = append(bList, branch.ID)
			cToB[branch.ClinicID] = bList
		}

		if err := tx.Create(&users).Error; err != nil {
			log.Fatal(err)
		}

		var sTemplate []lib.DentalClinicSpecialityTemplate
		var pTemplates []lib.DentalClinicProcedureTemplate
		// var procedures []lib.DentalClinicVisitProcedure
		// var visits []lib.DentalClinicVisit
		// var appointments []lib.DentalClinicAppointment
		var patients []lib.DentalClinicPatient
		var items []lib.DentalClinicInventoryItem

		for c := 0; c < len(clinics); c++ {
			specialitiesCount := rand.Intn(5) + 1
			for i := 0; i < specialitiesCount; i++ {
				sTemplate = append(sTemplate, lib.DentalClinicSpecialityTemplate{
					ClinicID:   clinics[c].ID,
					Speciality: faker.Name(),
				})

			}

		}
		if err := tx.Create(&sTemplate).Error; err != nil {
			log.Fatal(err)
		}

		for _, s := range sTemplate {
			proceduresCount := rand.Intn(5) + 1
			for p := 0; p < proceduresCount; p++ {
				pTemplates = append(pTemplates, lib.DentalClinicProcedureTemplate{
					ClinicID:     s.ClinicID,
					SpecialityID: s.ID,
					Procedure:    faker.Name(),
					Price:        rand.Float64() * 5000,
				})
			}
		}
		if err := tx.Create(&pTemplates).Error; err != nil {
			log.Fatal(err)
		}

		for c := 0; c < len(clinics); c++ {
			itemCount := rand.Intn(15)
			for i := 0; i < itemCount; i++ {
				items = append(items, lib.DentalClinicInventoryItem{
					ClinicID:        clinics[c].ID,
					Name:            faker.Name(),
					Quantity:        rand.Intn(100) + 1,
					WarningQuantity: rand.Intn(10) + 1,
					Unit:            faker.Word(),
				})
			}

			patientCount := rand.Intn(30) + 1
			for i := 0; i < patientCount; i++ {
				balance := 0.0
				if i%2 != 0 {
					balance = rand.Float64() * 15000 * -1
				}
				patients = append(patients, lib.DentalClinicPatient{
					ClinicID:       clinics[c].ID,
					Name:           faker.FirstName() + " " + faker.LastName(),
					PhoneNumber:    faker.Phonenumber(),
					Birthdate:      sql.NullString{String: faker.Date(), Valid: true},
					Balance:        balance,
					DentalHistory:  faker.Paragraph(),
					MedicalHistory: faker.Sentence(),
				})
			}
		}

		if err := tx.Create(&patients).Error; err != nil {
			log.Fatal(err)
		}

		// for b := 0; b < len(branches); b++ {
		// 	appointmentsCount := rand.Intn(10) + 1
		// 	for i := 0; i < appointmentsCount; i++ {
		// 		appointments = append(appointments, lib.DentalClinicAppointment{
		// 			BranchID: branches[b].ID,
		// 			ClinicID: branches[b].ClinicID,

		// 		})
		// 	}
		// }

		return nil
	})

	if err != nil {
		log.Fatal(err)
	}

}

func main() {
	log.Println("In Mock test")
	return
	insertMock()
}
