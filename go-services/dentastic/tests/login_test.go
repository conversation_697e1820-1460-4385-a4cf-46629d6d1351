package tests

import (
	"encoding/json"
	"io"
	"net/http/httptest"
	"strings"
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

type clinicCreateRequest struct {
	DisplayName string `json:"displayName"`
	Username    string `json:"username"`
	Name        string `json:"name"`
	Email       string `json:"email"`
	Password    string `json:"password"`
	PhoneNumber string `json:"phoneNumber"`
	SystemName  string `json:"systemName"`
	// Type        string `json:"type"`
	SubscriptionPlan string `json:"subscriptionPlan"`
}

type clinicLoginRequest struct {
	ClinicName string `json:"clinicName"`
	Username   string `json:"username"`
	Password   string `json:"password"`
}

func TestAddNewMerchant(t *testing.T) {

	plan := new(lib.DentalClinicSubscriptionPlan)

	if err := server.Db.First(plan).Error; err != nil {
		t.Error(err)
	}

	body, err := json.Marshal(clinicCreateRequest{
		DisplayName:      "Test Clinic",
		Username:         "test",
		Name:             "Test",
		Email:            "<EMAIL>",
		Password:         "password",
		PhoneNumber:      "0123456789",
		SystemName:       "testClinic",
		SubscriptionPlan: plan.ID,
	})
	if err != nil {
		t.Error(err)
	}

	req := httptest.NewRequest("POST", "/clinics", strings.NewReader(string(body)))
	req.Header.Set("Content-Type", "application/json")

	resp, _ := app.Test(req)
	if resp.StatusCode != 201 {
		rBody, _ := io.ReadAll(resp.Body)
		t.Errorf("Add new merchant failed. Expected status code 201 but found %d Body: %v", resp.StatusCode, string(rBody))
	}
}

func TestLoginWrongCredentials(t *testing.T) {
	body, err := json.Marshal(clinicLoginRequest{
		ClinicName: "Wrong",
		Username:   "test",
		Password:   "password",
	})
	if err != nil {
		t.Error(err)
	}

	req := httptest.NewRequest("POST", "/login", strings.NewReader(string(body)))
	req.Header.Set("Content-Type", "application/json")

	resp, _ := app.Test(req)
	if resp.StatusCode < 401 || resp.StatusCode > 404 {
		rBody, _ := io.ReadAll(resp.Body)
		t.Errorf("Login with wrong credentials returned a non-4XX response. Returned: %v: %v", resp.StatusCode, string(rBody))
	}
}

func TestLogin(t *testing.T) {
	body, err := json.Marshal(clinicLoginRequest{
		ClinicName: "testClinic",
		Username:   "test",
		Password:   "password",
	})
	if err != nil {
		t.Error(err)
	}

	req := httptest.NewRequest("POST", "/login", strings.NewReader(string(body)))
	req.Header.Set("Content-Type", "application/json")

	resp, _ := app.Test(req)
	if resp.StatusCode != 200 {
		t.Error("Failed to login with given credentials")
	}
}
