package tests

import (
	"encoding/json"
	"io"
	"net/http/httptest"
	"strings"
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

// Legacy test structures - keeping for backward compatibility
type clinicCreateRequest struct {
	DisplayName string `json:"displayName"`
	Username    string `json:"username"`
	Name        string `json:"name"`
	Email       string `json:"email"`
	Password    string `json:"password"`
	PhoneNumber string `json:"phoneNumber"`
	SystemName  string `json:"systemName"`
	// Type        string `json:"type"`
	SubscriptionPlan string `json:"subscriptionPlan"`
}

type clinicLoginRequest struct {
	ClinicName string `json:"clinicName"`
	Username   string `json:"username"`
	Password   string `json:"password"`
}

// Legacy tests - enhanced with better error handling and cleanup
func TestAddNewMerchant(t *testing.T) {
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	plan := new(lib.DentalClinicSubscriptionPlan)
	if err := server.Db.First(plan).Error; err != nil {
		t.Fatalf("Failed to get subscription plan: %v", err)
	}

	body, err := json.Marshal(clinicCreateRequest{
		DisplayName:      "Legacy Test Clinic",
		Username:         "legacytest",
		Name:             "Legacy Test User",
		Email:            "<EMAIL>",
		Password:         "password123",
		PhoneNumber:      "01234567890",
		SystemName:       "legacyTestClinic",
		SubscriptionPlan: plan.ID,
	})
	if err != nil {
		t.Fatalf("Failed to marshal request: %v", err)
	}

	req := httptest.NewRequest("POST", "/clinics", strings.NewReader(string(body)))
	req.Header.Set("Content-Type", "application/json")

	resp, err := app.Test(req)
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}

	if resp.StatusCode != 201 {
		rBody, _ := io.ReadAll(resp.Body)
		t.Errorf("Add new merchant failed. Expected status code 201 but found %d Body: %v", resp.StatusCode, string(rBody))
	}

	// Verify response contains token
	rBody, _ := io.ReadAll(resp.Body)
	var result map[string]interface{}
	if err := json.Unmarshal(rBody, &result); err != nil {
		t.Errorf("Response is not valid JSON: %v", err)
	} else if _, exists := result["token"]; !exists {
		t.Error("Response should contain token")
	}
}

func TestLoginWrongCredentials(t *testing.T) {
	body, err := json.Marshal(clinicLoginRequest{
		ClinicName: "NonExistentClinic",
		Username:   "test",
		Password:   "password",
	})
	if err != nil {
		t.Fatalf("Failed to marshal request: %v", err)
	}

	req := httptest.NewRequest("POST", "/login", strings.NewReader(string(body)))
	req.Header.Set("Content-Type", "application/json")

	resp, err := app.Test(req)
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}

	if resp.StatusCode < 401 || resp.StatusCode > 404 {
		rBody, _ := io.ReadAll(resp.Body)
		t.Errorf("Login with wrong credentials returned a non-4XX response. Returned: %v: %v", resp.StatusCode, string(rBody))
	}
}

func TestLogin(t *testing.T) {
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	// First create a clinic to login to
	clinic, user, _ := testData.CreateTestClinic("loginlegacytest")

	body, err := json.Marshal(clinicLoginRequest{
		ClinicName: clinic.Name,
		Username:   user.Username,
		Password:   "password", // This matches the test password in CreateTestClinic
	})
	if err != nil {
		t.Fatalf("Failed to marshal request: %v", err)
	}

	req := httptest.NewRequest("POST", "/login", strings.NewReader(string(body)))
	req.Header.Set("Content-Type", "application/json")

	resp, err := app.Test(req)
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}

	if resp.StatusCode != 200 {
		rBody, _ := io.ReadAll(resp.Body)
		t.Errorf("Failed to login with given credentials. Status: %d, Body: %s", resp.StatusCode, string(rBody))
	}

	// Verify response contains token
	rBody, _ := io.ReadAll(resp.Body)
	var result map[string]interface{}
	if err := json.Unmarshal(rBody, &result); err != nil {
		t.Errorf("Response is not valid JSON: %v", err)
	} else if _, exists := result["token"]; !exists {
		t.Error("Login response should contain token")
	}
}
