package tests

import (
	"fmt"
	"strings"
	"testing"

	"gitlab.com/payrows/dentastic/lib"
)

func TestLoginIntegration(t *testing.T) {
	client := NewTestClient(t)
	testData := NewTestData(t)
	defer testData.CleanupTestData()

	t.Run("Successful Login Flow", func(t *testing.T) {
		// Create test clinic using our test data factory
		clinic, user, _ := testData.CreateTestClinic("loginintegration")

		// Test login with correct credentials
		loginReq := LoginRequest{
			ClinicName: clinic.Name,
			Username:   user.Username,
			Password:   "password", // This is the test password we use
		}

		resp := client.Request("POST", "/login", loginReq, nil)
		if resp.Code != 200 {
			t.Fatalf("Login failed with status %d: %s", resp.Code, resp.Body.String())
		}

		// Verify response contains token
		result := client.AssertJSONResponse(resp, map[string]interface{}{})
		token, exists := result["token"]
		if !exists {
			t.Error("Login response should contain token")
		}

		// Verify token is a non-empty string
		tokenStr, ok := token.(string)
		if !ok || tokenStr == "" {
			t.Error("Token should be a non-empty string")
		}

		t.Logf("Login successful, received token: %s", tokenStr[:20]+"...")
	})

	t.Run("Login Validation Tests", func(t *testing.T) {
		// Create test clinic
		clinic, user, _ := testData.CreateTestClinic("validationtest")

		tests := []struct {
			name           string
			request        LoginRequest
			expectedStatus int
			description    string
		}{
			{
				name: "Valid Credentials",
				request: LoginRequest{
					ClinicName: clinic.Name,
					Username:   user.Username,
					Password:   "password",
				},
				expectedStatus: 200,
				description:    "Should succeed with correct credentials",
			},
			{
				name: "Wrong Password",
				request: LoginRequest{
					ClinicName: clinic.Name,
					Username:   user.Username,
					Password:   "wrongpassword",
				},
				expectedStatus: 401,
				description:    "Should fail with wrong password",
			},
			{
				name: "Wrong Username",
				request: LoginRequest{
					ClinicName: clinic.Name,
					Username:   "wronguser",
					Password:   "password",
				},
				expectedStatus: 401,
				description:    "Should fail with wrong username",
			},
			{
				name: "Wrong Clinic Name",
				request: LoginRequest{
					ClinicName: "wrongclinic",
					Username:   user.Username,
					Password:   "password",
				},
				expectedStatus: 404,
				description:    "Should fail with wrong clinic name",
			},
			{
				name: "Case Insensitive Clinic Name",
				request: LoginRequest{
					ClinicName: strings.ToUpper(clinic.Name),
					Username:   user.Username,
					Password:   "password",
				},
				expectedStatus: 200,
				description:    "Should work with different case clinic name",
			},
			{
				name: "Case Insensitive Username",
				request: LoginRequest{
					ClinicName: clinic.Name,
					Username:   strings.ToUpper(user.Username),
					Password:   "password",
				},
				expectedStatus: 200,
				description:    "Should work with different case username",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				resp := client.Request("POST", "/login", tt.request, nil)
				if resp.Code != tt.expectedStatus {
					t.Errorf("%s: Expected status %d, got %d. Response: %s",
						tt.description, tt.expectedStatus, resp.Code, resp.Body.String())
				}
			})
		}
	})

	t.Run("Input Validation", func(t *testing.T) {
		tests := []struct {
			name           string
			request        LoginRequest
			expectedStatus int
		}{
			{
				name: "Missing Clinic Name",
				request: LoginRequest{
					Username: "test",
					Password: "test",
				},
				expectedStatus: 400,
			},
			{
				name: "Empty Clinic Name",
				request: LoginRequest{
					ClinicName: "",
					Username:   "test",
					Password:   "test",
				},
				expectedStatus: 400,
			},
			{
				name: "Missing Username",
				request: LoginRequest{
					ClinicName: "test",
					Password:   "test",
				},
				expectedStatus: 400,
			},
			{
				name: "Empty Username",
				request: LoginRequest{
					ClinicName: "test",
					Username:   "",
					Password:   "test",
				},
				expectedStatus: 400,
			},
			{
				name: "Missing Password",
				request: LoginRequest{
					ClinicName: "test",
					Username:   "test",
				},
				expectedStatus: 400,
			},
			{
				name: "Empty Password",
				request: LoginRequest{
					ClinicName: "test",
					Username:   "test",
					Password:   "",
				},
				expectedStatus: 400,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				resp := client.Request("POST", "/login", tt.request, nil)
				if resp.Code != tt.expectedStatus {
					t.Errorf("Expected status %d for %s, got %d", tt.expectedStatus, tt.name, resp.Code)
				}
			})
		}
	})

	t.Run("Multiple Users Same Clinic", func(t *testing.T) {
		// Create test clinic with multiple users
		clinic, masterUser, _ := testData.CreateTestClinic("multiuser")
		adminUser := testData.CreateTestUser(clinic.ID, "Admin User", "adminuser", "admin")
		basicUser := testData.CreateTestUser(clinic.ID, "Basic User", "basicuser", "basic")

		// Test login for each user
		users := []struct {
			name string
			user *lib.DentalClinicUser
		}{
			{"Master User", masterUser},
			{"Admin User", adminUser},
			{"Basic User", basicUser},
		}

		for _, u := range users {
			t.Run(u.name, func(t *testing.T) {
				loginReq := LoginRequest{
					ClinicName: clinic.Name,
					Username:   u.user.Username,
					Password:   "password",
				}

				resp := client.Request("POST", "/login", loginReq, nil)
				if resp.Code != 200 {
					t.Errorf("Login failed for %s with status %d: %s", u.name, resp.Code, resp.Body.String())
				}

				// Verify token is returned
				result := client.AssertJSONResponse(resp, map[string]interface{}{})
				if _, exists := result["token"]; !exists {
					t.Errorf("Token not returned for %s", u.name)
				}
			})
		}
	})

	t.Run("Concurrent Login Requests", func(t *testing.T) {
		// Create test clinic
		clinic, user, _ := testData.CreateTestClinic("concurrent")

		// Test concurrent login requests
		done := make(chan bool, 5)
		errors := make(chan error, 5)

		for i := 0; i < 5; i++ {
			go func(id int) {
				loginReq := LoginRequest{
					ClinicName: clinic.Name,
					Username:   user.Username,
					Password:   "password",
				}

				resp := client.Request("POST", "/login", loginReq, nil)
				if resp.Code != 200 {
					errors <- fmt.Errorf("Concurrent login %d failed with status %d", id, resp.Code)
				} else {
					// Verify token is returned
					result := client.AssertJSONResponse(resp, map[string]interface{}{})
					if _, exists := result["token"]; !exists {
						errors <- fmt.Errorf("Token not returned for concurrent login %d", id)
					}
				}
				done <- true
			}(i)
		}

		// Wait for all goroutines to complete
		for i := 0; i < 5; i++ {
			<-done
		}

		// Check for errors
		close(errors)
		for err := range errors {
			t.Error(err)
		}
	})
}
