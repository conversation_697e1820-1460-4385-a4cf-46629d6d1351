name: Dentastic Integration Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'go-services/dentastic/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'go-services/dentastic/**'
  workflow_dispatch:

jobs:
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        go-version: [1.21, 1.22]
    
    defaults:
      run:
        working-directory: go-services/dentastic
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Go ${{ matrix.go-version }}
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}
        
    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ matrix.go-version }}-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-${{ matrix.go-version }}-
          
    - name: Install dependencies
      run: go mod download
      
    - name: Verify dependencies
      run: go mod verify
      
    - name: Set up test environment
      run: |
        # Set environment variables for testing
        echo "DB_URL=sqlite:///:memory:" >> $GITHUB_ENV
        echo "JWT_SECRET=test_jwt_secret_for_ci" >> $GITHUB_ENV
        echo "DISABLE_STRIPE=true" >> $GITHUB_ENV
        echo "DISABLE_S3=true" >> $GITHUB_ENV
        echo "DISABLE_EMAIL=true" >> $GITHUB_ENV
        echo "DISABLE_SMS=true" >> $GITHUB_ENV
        
    - name: Run linting
      run: |
        go fmt ./...
        go vet ./...
        
    - name: Run basic tests
      run: go test ./tests/basic_test.go ./tests/main_test.go ./tests/test_utils.go ./tests/test_routes_patch.go -v
      
    - name: Run login integration tests
      run: go test ./tests/login_integration_test.go ./tests/main_test.go ./tests/test_utils.go ./tests/test_routes_patch.go -v
      
    - name: Run clinic registration tests
      run: go test ./tests/clinic_registration_test.go ./tests/main_test.go ./tests/test_utils.go ./tests/test_routes_patch.go -v
      
    - name: Run end-to-end tests
      run: go test ./tests/end_to_end_test.go ./tests/main_test.go ./tests/test_utils.go ./tests/test_routes_patch.go -v
      
    - name: Run complete test suite
      run: go test ./tests/ -v
      
    - name: Run test script
      run: ./scripts/run-tests.sh
      
    - name: Generate test coverage
      run: |
        go test ./tests/ -coverprofile=coverage.out -covermode=atomic
        go tool cover -html=coverage.out -o coverage.html
        
    - name: Upload coverage reports
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report-go${{ matrix.go-version }}
        path: |
          go-services/dentastic/coverage.out
          go-services/dentastic/coverage.html
          
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-go${{ matrix.go-version }}
        path: go-services/dentastic/test-results.xml

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: integration-tests
    
    defaults:
      run:
        working-directory: go-services/dentastic
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.22
        
    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: '-fmt sarif -out results.sarif ./...'
        
    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: results.sarif

  performance-test:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    defaults:
      run:
        working-directory: go-services/dentastic
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.22
        
    - name: Install dependencies
      run: go mod download
      
    - name: Run benchmark tests
      run: |
        go test ./tests/ -bench=. -benchmem -run=^$ > benchmark-results.txt
        cat benchmark-results.txt
        
    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: go-services/dentastic/benchmark-results.txt

  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [integration-tests, security-scan]
    if: always()
    
    steps:
    - name: Notify Success
      if: needs.integration-tests.result == 'success' && needs.security-scan.result == 'success'
      run: |
        echo "✅ All Dentastic integration tests passed successfully!"
        echo "🔒 Security scan completed without issues"
        echo "🎉 Ready for deployment"
        
    - name: Notify Failure
      if: needs.integration-tests.result == 'failure' || needs.security-scan.result == 'failure'
      run: |
        echo "❌ Some tests failed or security issues found"
        echo "🔍 Please check the test results and fix any issues"
        exit 1
