package main

import (
	"flag"
	"log"

	"github.com/joho/godotenv"
	lib "gitlab.com/payrows/dentastic/lib"
	"gitlab.com/payrows/dentastic/lib/admin"
)

func main() {
	err := godotenv.Load()
	if err != nil {
		log.Println("Error loading .env file")
	}

	shouldMigrate := flag.Bool("migrate", false, "Defines if the system should start with a migration or not")
	exitAfterMigrate := flag.Bool("exit-after-migrate", false, "Instructs the app to stop the process after migration is completed")
	migrateMerchant := flag.Bool("migrate-merchant", false, "Ask the database to migrate the merchant table")
	flag.Parse()

	log.SetFlags(log.Llongfile)

	initConfig := lib.IntializationConfig{
		ExitAfterMigrate: *exitAfterMigrate,
		ShouldMigrate:    *shouldMigrate,
		MigrateMerchant:  *migrateMerchant,
	}
	app, server := lib.Initialize(initConfig)

	err = admin.Initialize(server)
	if err != nil {
		log.Fatalln(err)
	}

	err = app.Listen(":" + server.Port)
	if err != nil {
		log.Fatalln(err)
	}
}
