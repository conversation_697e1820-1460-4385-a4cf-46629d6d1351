#!/bin/bash

# Dentastic Integration Test Runner
# This script runs the comprehensive integration test suite for the Dentastic application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run tests with proper error handling
run_test_suite() {
    local test_name="$1"
    local test_files="$2"
    
    print_status "Running $test_name..."
    
    if go test $test_files -v; then
        print_success "$test_name completed successfully"
        return 0
    else
        print_error "$test_name failed"
        return 1
    fi
}

# Main execution
main() {
    print_status "Starting Dentastic Integration Test Suite"
    print_status "========================================"
    
    # Check if we're in the right directory
    if [[ ! -f "go.mod" ]] || [[ ! -d "tests" ]]; then
        print_error "Please run this script from the dentastic project root directory"
        exit 1
    fi
    
    # Set environment variables for testing
    export DB_URL="sqlite:///:memory:"
    export JWT_SECRET="test_jwt_secret_for_integration_tests"
    export DISABLE_STRIPE="true"
    export DISABLE_S3="true"
    export DISABLE_EMAIL="true"
    export DISABLE_SMS="true"
    
    print_status "Environment configured for testing"
    
    # Test files configuration
    COMMON_FILES="./tests/main_test.go ./tests/test_utils.go ./tests/test_routes_patch.go"
    
    # Track test results
    TOTAL_TESTS=0
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    # Run individual test suites
    test_suites=(
        "Basic Database Operations:./tests/basic_test.go $COMMON_FILES"
        "Login Integration Tests:./tests/login_integration_test.go $COMMON_FILES"
        "Clinic Registration Tests:./tests/clinic_registration_test.go $COMMON_FILES"
        "End-to-End Workflow Tests:./tests/end_to_end_test.go $COMMON_FILES"
    )
    
    for suite in "${test_suites[@]}"; do
        IFS=':' read -r name files <<< "$suite"
        TOTAL_TESTS=$((TOTAL_TESTS + 1))
        
        if run_test_suite "$name" "$files"; then
            PASSED_TESTS=$((PASSED_TESTS + 1))
        else
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
        echo ""
    done
    
    # Run comprehensive test suite
    print_status "Running Complete Test Suite..."
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if go test ./tests/ -v; then
        print_success "Complete test suite passed"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        print_error "Complete test suite failed"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
    
    # Print summary
    echo ""
    print_status "Test Summary"
    print_status "============"
    echo -e "Total Test Suites: $TOTAL_TESTS"
    echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
    echo -e "${RED}Failed: $FAILED_TESTS${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        print_success "All tests passed! 🎉"
        echo ""
        print_status "Test Coverage Summary:"
        echo "✅ Database Operations & Data Validation"
        echo "✅ Authentication & Login Flows"
        echo "✅ Clinic Registration & Validation"
        echo "✅ End-to-End Workflows"
        echo "✅ SQLite Compatibility"
        echo "✅ Error Handling & Edge Cases"
        echo "✅ Concurrent Operations"
        echo "✅ Input Validation"
        echo ""
        exit 0
    else
        print_error "Some tests failed. Please check the output above."
        exit 1
    fi
}

# Help function
show_help() {
    echo "Dentastic Integration Test Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -q, --quiet    Run tests in quiet mode"
    echo "  -v, --verbose  Run tests in verbose mode (default)"
    echo ""
    echo "Examples:"
    echo "  $0              # Run all tests"
    echo "  $0 --quiet      # Run tests with minimal output"
    echo ""
    echo "Environment Variables:"
    echo "  DB_URL         Database URL (defaults to SQLite in-memory)"
    echo "  JWT_SECRET     JWT secret for testing"
    echo ""
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -q|--quiet)
            QUIET=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main
