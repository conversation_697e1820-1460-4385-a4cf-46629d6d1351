package main

import (
	"fmt"
	"log"
	"time"

	"gitlab.com/payrows/dentastic/lib"
	"gitlab.com/payrows/messaging/fcm"
)

func main() {
	db, err := lib.InitializeDB()
	if err != nil {
		panic(err)
	}
	startTime := time.Now()
	endTime := startTime.Add(time.Hour * 1)
	var result []map[string]interface{}
	db.Raw(`
		SELECT dcu.id, dcu.FCMToken, dca.start_time
		FROM dental_clinic_users dcu
		INNER JOIN dental_clinic_appointments dca ON dca.dentist_id = dcu.id
		WHERE dca.start_time >= ? AND dca.start_time <= ?
	`, startTime, endTime).Scan(&result)

	failed := 0
	for _, r := range result {
		fcmToken := r["FCMToken"].(string)
		if fcmToken == "" {
			failed++
			continue
		}
		notification := &fcm.FCMRequest{
			Recipients: []string{fcmToken},
			Title:      "Upcoming Appointment",
			Body:       fmt.Sprintf("You have an appointment in 1 hour at %v", r["start_time"]),
		}
		if err := fcm.SendFCM(*notification); err != nil {
			failed++
			log.Println("Failed to send FCM to: " + fcmToken + " with error: " + err.Error())
		}
	}
	log.Printf("Sent FCM succeeded: %v. Failed: %v\n", len(result)-failed, failed)
}
