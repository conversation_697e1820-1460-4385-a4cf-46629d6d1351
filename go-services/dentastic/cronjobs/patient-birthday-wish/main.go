package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"gitlab.com/payrows/dentastic/lib"
	messaging "gitlab.com/payrows/messaging/sms"
)

const (
	birthdayMessageAR = "عيد ميلاد سعيد %s! نتمنى لك يوما رائعا مليئا بالابتسامات من %s."
	birthdayMessageEN = "Happy Birthday %s! Wishing you a wonderful day filled with smiles from %s."
)

func main() {
	db, err := lib.InitializeDB()
	if err != nil {
		panic(err)
	}

	today := time.Now().Format("2006-01-02")
	monthDay := time.Now().Format("-01-02")

	log.Printf("Starting birthday wish cronjob for date: %s\n", today)

	var birthdayConfigs []lib.DentalClinicBirthdayWishConfig

	// Use GORM's idiomatic approach to query and preload related model
	if err := db.Preload("Clinic").
		Where("birthday_wish IS NOT NULL AND birthday_wish != ?", "").
		Find(&birthdayConfigs).Error; err != nil {
		log.Fatalf("Failed to get birthday wish configs: %v", err)
	}

	log.Printf("Found %d clinics with birthday wish enabled\n", len(birthdayConfigs))

	totalSent := 0
	totalFailed := 0

	for _, config := range birthdayConfigs {
		clinic := config.Clinic
		log.Printf("Processing clinic: %s (%s)\n", clinic.Name, clinic.ID)

		// Check SMS quota
		var smsQuota lib.DentalClinicSMSQuota
		if err := db.Where("clinic_id = ?", clinic.ID).Take(&smsQuota).Error; err != nil {
			log.Printf("Failed to get SMS quota for clinic %s: %v", clinic.ID, err)
			continue
		}

		// Check if quota is expired
		if time.Now().After(smsQuota.PackageExpiryDate) {
			log.Printf("SMS quota package expired for clinic %s", clinic.ID)
			continue
		}

		// Check if quota is more than 0
		if smsQuota.RemainingLocalSMS <= 0 {
			log.Printf("No SMS quota available for clinic %s", clinic.ID)
			continue
		}

		var patients []lib.DentalClinicPatient
		// TODO: check if there is a better way to do this
		if err := db.Where("clinic_id = ? AND phone_number != '' AND CAST(birthdate AS TEXT) LIKE ?",
			clinic.ID, "%"+monthDay).Find(&patients).Error; err != nil {
			log.Printf("Failed to get patients for clinic %s: %v", clinic.ID, err)
			continue
		}

		log.Printf("Found %d patients with birthday today for clinic %s\n", len(patients), clinic.ID)

		// TODO: we can ignore that and send anyway
		// Check if we have enough quota
		if len(patients) > smsQuota.RemainingLocalSMS {
			log.Printf("Insufficient SMS quota for clinic %s: needed %d, available %d",
				clinic.ID, len(patients), smsQuota.RemainingLocalSMS)
			continue
		}

		var quotaMutex sync.Mutex
		var wg sync.WaitGroup
		clinicSent := 0
		clinicFailed := 0

		messageTemplate := birthdayMessageEN
		if lib.DentalClinicBirthdayWish(config.BirthdayWish.String) == lib.DentailClinicBirthdayWishEnabledAR {
			messageTemplate = birthdayMessageAR
		}

		for _, patient := range patients {
			if patient.PhoneNumber == "" {
				continue
			}

			wg.Add(1)
			go func(p lib.DentalClinicPatient) {
				defer wg.Done()

				personalizedMessage := fmt.Sprintf(messageTemplate, p.Name, clinic.Name)

				err := messaging.SendSMS(&messaging.SMSRequest{
					Recipient: p.PhoneNumber,
					Body:      personalizedMessage,
				})

				if err != nil {
					log.Printf("Failed to send SMS to %s: %v", p.PhoneNumber, err)
					clinicFailed++
				} else {
					// TODO: add forign SMS price and currency
					comm := &lib.DentalClinicCommunication{
						ClinicID:       clinic.ID,
						Body:           personalizedMessage,
						Recipient:      p.PhoneNumber,
						Method:         "SMS",
						Price:          0.30,
						Currency:       "EGP",
						TargetingGroup: "Birthday", // TODO
					}

					if err := db.Create(comm).Error; err != nil {
						log.Printf("Failed to create communication record: %v", err)
					}

					quotaMutex.Lock()
					smsQuota.RemainingLocalSMS--
					if err := db.Save(&smsQuota).Error; err != nil {
						log.Printf("Failed to update SMS quota: %v", err)
					}
					quotaMutex.Unlock()
					clinicSent++
				}
			}(patient)
		}
		wg.Wait()

		log.Printf("Clinic %s: Sent %d birthday wishes, Failed %d\n",
			clinic.ID, clinicSent, clinicFailed)

		totalSent += clinicSent
		totalFailed += clinicFailed
	}

	log.Printf("Birthday wish cronjob completed. Total sent: %d, Total failed: %d\n",
		totalSent, totalFailed)
}
