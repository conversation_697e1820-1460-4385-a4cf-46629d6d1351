package main

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/stripe/stripe-go/v74"
	"github.com/stripe/stripe-go/v74/customer"
	"github.com/stripe/stripe-go/v74/paymentintent"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/dentastic/lib"
	"gorm.io/gorm"
)

func main() {
	db, err := lib.InitializeDB()
	if err != nil {
		panic(err)
	}
	sqldb, err := db.DB()
	if err != nil {
		panic(err)
	}
	defer sqldb.Close()
	now := time.Now()
	aMonthAgo := now.AddDate(0, -1, 0)
	var clinicIds []string
	if err := db.Model(&lib.DentalClinic{}).Where("subscription_renewed_at <= ? OR subscription_renewed_at IS NULL", lib.FormatDateToString(aMonthAgo)).Pluck("id", &clinicIds).Error; err != nil {
		panic(err)
	}
	log.Println("Selected clinics for renewal:", clinicIds)
	for _, clinicId := range clinicIds {
		clinic := new(lib.DentalClinic)
		if err := db.Where("id = ?", clinicId).First(&clinic).Error; err != nil {
			panic(err)
		}
		err := db.Transaction(func(tx *gorm.DB) error {
			if err := RenewSubscription(tx, clinic, now); err != nil {
				return err
			}
			if err := ChargeClinic(tx, clinic, now); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			log.Println("Skipping renewal for clinic ", clinicId, " . Error:", err)
		}
	}
	log.Println("Renewed subscriptions for ", len(clinicIds), " clinics.")
}

func ChargeClinic(db *gorm.DB, clinic *lib.DentalClinic, now time.Time) error {
	paymentMethod := new(models.MerchantPaymentMethod)
	if err := db.Where("merchant_id = ?", clinic.MerchantID).First(&paymentMethod).Error; err != nil {
		return err
	}
	// NOTE: We're only using stripe for now
	if paymentMethod.Provider != "stripe" {
		return errors.New("unhandled payment method provider")
	}
	// merchantCard, err := card.Get(paymentMethod.ProviderRef, nil)
	// if err != nil {
	// 	return errors.New("failed to get merchant card:" + err.Error())
	// }
	stripeCustomer := customer.Search(&stripe.CustomerSearchParams{
		SearchParams: stripe.SearchParams{
			Single: true,
			Query:  "merchant_id:" + clinic.MerchantID,
		},
	})
	if stripeCustomer == nil {
		return errors.New("failed to find stripe customer")
	}
	clinicSub := new(lib.DentalClinicSubscriptionPlan)
	if err := db.Where("clinic_id = ?", clinic.ID).First(clinicSub).Error; err != nil {
		return err
	}
	count, err := CountClinicVisitUsage(db, clinic)
	if err != nil {
		return err
	}
	rate := clinicSub.PricePerVisit
	amount := float64(count) * rate
	pi, err := paymentintent.New(&stripe.PaymentIntentParams{
		Amount: stripe.Int64(int64(amount * 100)),
		// Currency:      stripe.String(string(stripe.CurrencyUSD)),
		Currency:      stripe.String(string(strings.ToLower(clinicSub.Currency))),
		PaymentMethod: &paymentMethod.ProviderRef,
		Customer:      &stripeCustomer.Customer().ID,
		Params: stripe.Params{
			IdempotencyKey: stripe.String(clinic.MerchantID + "-" + fmt.Sprint(now.Unix())),
			Metadata: map[string]string{
				"merchant_id": clinic.MerchantID,
			},
		},
		StatementDescriptor: stripe.String("NAAB"),
		Confirm:             stripe.Bool(true),
		OffSession:          stripe.Bool(true),
	})
	if err != nil {
		return errors.New("failed to create payment intent:" + err.Error())
	}
	piJSON, err := json.Marshal(pi)
	if err != nil {
		log.Println("failed to marshal payment intent:" + err.Error())
	}
	log.Println(string(piJSON))
	return nil
}

func RenewSubscription(db *gorm.DB, clinic *lib.DentalClinic, now time.Time) error {
	clinic.SubscriptionRenewedAt = now.Format(time.RFC3339)
	if err := db.Model(&clinic).UpdateColumn("subscription_renewed_at", clinic.SubscriptionRenewedAt).Error; err != nil {
		return errors.New("failed to update clinic subscription renewed at date:" + err.Error())
	}
	return nil
}

func CountClinicVisitUsage(db *gorm.DB, clinic *lib.DentalClinic) (int64, error) {
	var count int64 = 0
	if err := db.Where("clinic_id = ? AND created_at > ?", clinic.ID, clinic.SubscriptionRenewedAt).Model(&lib.DentalClinicVisit{}).Count(&count).Error; err != nil {
		return 0, errors.New("failed to count clinic visits:" + err.Error())
	}
	return count, nil
}
