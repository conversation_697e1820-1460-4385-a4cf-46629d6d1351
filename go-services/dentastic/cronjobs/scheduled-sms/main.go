package main

import (
	"fmt"
	"log"
	"os"
	"reflect"
	"strings"
	"time"

	coreHelpers "gitlab.com/payrows/core/helpers"
	coreMessaging "gitlab.com/payrows/core/messaging"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

func main() {
	dbUrl := coreHelpers.EnvVarOrDefault("DB_URL", "postgresql://postgres:password@localhost:5432/payrows_dentastic")
	db, err := gorm.Open(postgres.Open(dbUrl), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
		Logger: gormLogger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			gormLogger.Config{
				Colorful:                  true,
				IgnoreRecordNotFoundError: true,
			},
		),
	})
	if err != nil {
		log.Fatalln(err)
	}
	log.Println("Connected to " + dbUrl)

	err = sendSmsToPatientsWithAppointments(db)
	if err != nil {
		panic(err)
	}
}

func sendSmsToPatientsWithAppointments(db *gorm.DB) error {
	now := time.Now()
	// TODO: should handle when client is not in egypt
	location := time.FixedZone("UTC+2", 2*60*60)
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
	startTime = startTime.Add(24 * time.Hour)
	endTime := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 59, location)

	var patientsWithAppointments []map[string]interface{}
	if err := db.Raw(`
		SELECT p.name AS patient_name, p.phone_number AS patient_phone_number, p.id AS patient_id, a.start_time AS start_time, 
					c.id AS clinic_id, c.display_name AS clinic_display_name, c.country as clinic_country, c.currency AS clinic_currency, 
					c.unifonic_sender_id AS clinic_sender_id, b.name AS branch_name, b.longitude, b.latitude 
		FROM dental_clinic_patients AS p
		INNER JOIN dental_clinic_appointments AS a ON p.id = a.patient_id
		INNER JOIN dental_clinics AS c ON p.clinic_id = c.id
		INNER JOIN dental_clinic_branches AS b ON a.branch_id = b.id
		WHERE a.start_time >= ? AND a.end_time <= ?
	`, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339)).Scan(&patientsWithAppointments).Error; err != nil {
		return err
	}
	log.Println(patientsWithAppointments)
	clinicSmsesUsed := map[string]int{}
	for _, v := range patientsWithAppointments {
		_, ok := clinicSmsesUsed[v["clinic_id"].(string)]
		if !ok {
			clinicSmsesUsed[v["clinic_id"].(string)] = 0
		}
	}
	log.Println(reflect.TypeOf(patientsWithAppointments[0]["clinic_sender_id"]))
	for _, v := range patientsWithAppointments {
		senderId := ""
		if v["clinic_sender_id"] != nil {
			senderId = v["clinic_sender_id"].(string)
		}
		recepient := v["patient_phone_number"].(string)
		recepient = strings.TrimPrefix(recepient, "+")
		recepient = strings.TrimPrefix(recepient, "00")
		if strings.HasPrefix(recepient, "0") {
			recepient = "2" + recepient
		}
		appointmentTime := v["start_time"].(time.Time)
		body := fmt.Sprintf(
			"Hi %s, this is %s. We would like to remind you of your appointment tomorrow %d/%d at %v",
			v["patient_name"],
			v["clinic_display_name"],
			appointmentTime.Day(),
			appointmentTime.Month(),
			appointmentTime.Format("15:04"),
		)
		log.Println(body)
		err := coreMessaging.SendSMS(coreMessaging.SMSRequest{
			SenderID:  senderId,
			Recipient: recepient,
			Body:      body,
		})
		if err != nil {
			log.Println(err)
			continue
		}
		clinicSmsesUsed[v["clinic_id"].(string)] += 1
	}
	log.Println(clinicSmsesUsed)
	return nil
}
