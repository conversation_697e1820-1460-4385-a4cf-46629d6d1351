package main

import (
	"fmt"
	"log"
	"time"

	"gitlab.com/payrows/dentastic/lib"
	"gitlab.com/payrows/messaging/fcm"
)

func main() {
	db, err := lib.InitializeDB()
	if err != nil {
		panic(err)
	}
	startTime := time.Now()
	// endTime := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 59, startTime.Location()) // end of the day
	endTime := startTime.Add(time.Hour * 18) //  Cron will be triggered between 8-11 AM and we need to cover after midnight appointments
	var result []map[string]interface{}
	if err := db.Raw(`
		SELECT dcu.id, dcu.fcm_token, COUNT(dca.id) AS appointments_count
		FROM dental_clinic_users dcu
		INNER JOIN dental_clinic_appointments dca ON dca.dentist_id = dcu.id
		WHERE dca.start_time >= ? AND dca.start_time <= ?
		GROUP BY dcu.id
	`, startTime, endTime).Scan(&result).Error; err != nil {
		log.Println("Failed to get appointments for today with error: " + err.Error())
	}

	failed := 0
	skipped := 0
	for _, r := range result {
		// This might actually be unnecessary since the inner join should guarantee that there is an appointment
		if r["appointments_count"].(int64) == 0 {
			skipped++
			continue
		}
		fcmToken := r["fcm_token"].(string)
		if fcmToken == "" {
			failed++
			continue
		}
		notification := &fcm.FCMRequest{
			Recipients: []string{fcmToken},
			Title:      "Today's Schedule",
			Body:       fmt.Sprintf("You have %d appointment(s) today", r["appointments_count"].(int64)),
		}
		if err := fcm.SendFCM(*notification); err != nil {
			failed++
			log.Println("Failed to send FCM to: " + fcmToken + " with error: " + err.Error())
		}
	}
	log.Printf("Sent FCM succeeded: %v. Failed: %v. Skipped: %v\n", len(result)-failed-skipped, failed, skipped)
}
