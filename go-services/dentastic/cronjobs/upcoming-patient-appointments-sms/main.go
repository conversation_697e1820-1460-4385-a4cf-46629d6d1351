package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/helpers"
	"gitlab.com/payrows/dentastic/lib"
)

func main() {
	db, err := lib.InitializeDB()
	if err != nil {
		panic(err)
	}
	host := helpers.EnvVarOrDefault(config.GetServiceEnv(config.MessagingService), "http://localhost:3000")
	clinicNotificationConfigs := []lib.DentalClinicPatientNotificationConfig{}
	if err := db.Preload("Clinic").Find(&clinicNotificationConfigs).Error; err != nil {
		panic(err)
	}
	for _, cnc := range clinicNotificationConfigs {
		now := time.Now()
		then := now.Add(time.Hour * 24 * time.Duration(cnc.DaysPrior))
		start := time.Date(then.Year(), then.Month(), then.Day(), 0, 0, 0, 0, then.Location())
		end := time.Date(then.Year(), then.Month(), then.Day(), 23, 59, 59, 0, then.Location())
		appointments := []lib.DentalClinicAppointment{}
		if err := db.Where("dental_clinic_id = ? AND start_time >= ? AND start_time <= ?", cnc.ClinicID, start, end).Preload("Patient").Find(&appointments).Error; err != nil {
			panic(err)
		}
		for _, appointment := range appointments {
			smsParams := &sendSmsParams{
				To:      appointment.Patient.PhoneNumber,
				Message: "",
				Host:    host + "/sms",
			}
			switch cnc.Language {
			case lib.EnglishDentalClinicSupportedNotificationLanguage:
				smsParams.Message = "Hi " + appointment.Patient.Name + ",\nThis is a reminder that you have an appointment with " + cnc.Clinic.DisplayName + " on " + appointment.StartTime.Format("2006-01-02 15:04:05") + "."
			case lib.ArabicDentalClinicSupportedNotificationLanguage:
				smsParams.Message = "مرحبا " + appointment.Patient.Name + "\nنذكرك بموعد مع " + cnc.Clinic.DisplayName + " في تمام الساعة " + appointment.StartTime.Format("2006-01-02 15:04:05") + "."
			default:
				log.Println("Unsupported language: " + string(cnc.Language) + " for clinic: " + cnc.Clinic.DisplayName)
				continue
			}
			if cnc.IncludeLocation && appointment.Branch.Latitude.Valid && appointment.Branch.Longitude.Valid {
				smsParams.Message += fmt.Sprintf("\n\n https://maps.google.com/?q=%v,%v", appointment.Branch.Latitude.Float64, appointment.Branch.Longitude.Float64)
			}
			if err := sendSms(smsParams); err != nil {
				log.Println("Failed to send sms to :", smsParams.To, err.Error())
			}
		}
	}
}

type sendSmsParams struct {
	To      string `json:"to"`
	Message string `json:"message"`
	Host    string `json:"-"`
}

func sendSms(params *sendSmsParams) error {
	body, err := json.Marshal(params)
	if err != nil {
		return err
	}
	if err != nil {
		return err
	}
	client := http.DefaultClient
	req, err := http.NewRequest("POST", params.Host, bytes.NewBuffer(body))
	if err != nil {
		return err
	}
	res, err := client.Do(req)
	if err != nil {
		return err
	}
	if res.StatusCode != 200 {
		return fmt.Errorf("Failed to send sms: %v", res.StatusCode)
	}
	return nil
}
