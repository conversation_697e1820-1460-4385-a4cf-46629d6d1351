package lib

import (
	"encoding/json"
	"errors"
	"log"

	"github.com/gofiber/fiber/v2"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type JsonUser struct {
	DentalClinicUser
	Percentage *float64 `json:"percentage,omitempty"`
	Clinic     string   `json:"clinic,omitempty"`
	Password   string   `json:"password,omitempty"`
}

type JsonDentalClinic struct {
	DentalClinic
	Merchant         string `json:"merchant,omitempty"`
	UnifonicSenderId string `json:"unifonicSenderId,omitempty"`
	SubscriptionPlan string `json:"subscriptionPlan,omitempty"`
}

type JsonDentalClinicPatientNotificationConfig struct {
	DentalClinicPatientNotificationConfig
	Clinic string `json:"clinic,omitempty"`
}

func isValidUserRole(role string) bool {
	switch role {
	case
		string(DentalClinicRoleMaster),
		string(DentalClinicRoleAdmin),
		string(DentalClinicRoleSecretary),
		string(DentalClinicRoleExternal),
		string(DentalClinicRoleBasic):
		return true
	default:
		return false
	}
}

// GET: /users
func HandleUsersGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		var users []DentalClinicUser
		if err := server.Db.Where("clinic_id = ? AND deleted_at IS NULL", user.ClinicID).Find(&users).Error; err != nil {
			panic(err)
		}
		jsonUsers := make([]JsonUser, len(users))
		for index, v := range users {
			jsonUsers[index] = JsonUser{DentalClinicUser: v}
			if user.Role != DentalClinicRoleMaster {
				jsonUsers[index].Percentage = nil
			}
		}
		return c.JSON(map[string]interface{}{"users": jsonUsers})
	}
}

// POST: /users
func HandleUserCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name        string   `json:"name"`
		Username    string   `json:"username"`
		Password    string   `json:"password"`
		PhoneNumber string   `json:"phoneNumber"`
		Role        string   `json:"role"`
		IsDentist   bool     `json:"isDentist"`
		Percentage  *float64 `json:"percentage,omitempty"`
		HourlyRate  *float64 `json:"hourlyRate,omitempty"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		clinic := new(DentalClinic)
		if err = server.Db.Preload("SubscriptionPlan").Where("id = ?", user.ClinicID).Take(clinic).Error; err != nil {
			panic(err)
		}
		// if !clinic.SubscriptionPlan.CanAddUsers {
		// 	return responses.Send400(c, "Your current subscription plan does not allow adding more users")
		// }
		log.Println("Request Body:", body)
		if len(body.Username) == 0 {
			return responses.Send400(c, "Missing username")
		}
		newUser := new(DentalClinicUser)
		if err = server.Db.Where("username ILIKE ? AND clinic_id = ? AND deleted_at IS NULL", body.Username, user.ClinicID).Take(newUser).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			panic(err)
		}
		if len(newUser.ID) != 0 {
			return responses.Send400(c, "User with the same username already exists")
		}
		if !isValidUserRole(body.Role) {
			return responses.Send400(c, "Invalid user role")
		}
		if len(body.Password) < 6 {
			return responses.Send400(c, "Initial user password is too short")
		}
		if len(body.Name) == 0 {
			return responses.Send400(c, "Name is not provided")
		}
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(body.Password), 10)
		if err != nil {
			panic(err)
		}
		newUser.ClinicID = user.ClinicID
		newUser.Name = body.Name
		newUser.Username = body.Username
		newUser.Password = string(hashedPassword)
		newUser.PhoneNumber = body.PhoneNumber
		newUser.IsDentist = body.IsDentist
		newUser.Role = DentalClinicRole(body.Role)
		if body.Percentage != nil && user.Role == DentalClinicRoleMaster {
			newUser.Percentage = *body.Percentage
		}
		if body.HourlyRate != nil && user.Role == DentalClinicRoleMaster {
			newUser.HourlyRate = *body.HourlyRate
		}

		if err = server.Db.Create(newUser).Error; err != nil {
			panic(err)
		}

		newUserJson := JsonUser{DentalClinicUser: *newUser}

		return c.Status(201).JSON(map[string]interface{}{"user": newUserJson})
	}
}

// GET: /users/me
func HandleUserProfileGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		// log.Println("Get User Profile Context:", c.UserContext().Value("user"))
		user := c.UserContext().Value("user").(DentalClinicUser)
		clinic := new(DentalClinic)
		if err := server.Db.Where("id = ?", user.ClinicID).Take(clinic).Error; err != nil {
			panic(err)
		}
		clinicPatientNotificationConfig := new(DentalClinicPatientNotificationConfig)
		if err := server.Db.Where("clinic_id = ?", user.ClinicID).Take(clinicPatientNotificationConfig).Error; err != nil {
			if !errors.Is(gorm.ErrRecordNotFound, err) {
				panic(err)
			}
		}
		var percentage *float64
		if user.Role == DentalClinicRoleMaster {
			percentage = &user.Percentage
		}
		userJson := JsonUser{DentalClinicUser: user, Percentage: percentage}
		clinicJson := JsonDentalClinic{DentalClinic: *clinic}
		clinicPatientNotificationConfigJson := JsonDentalClinicPatientNotificationConfig{DentalClinicPatientNotificationConfig: *clinicPatientNotificationConfig}
		return c.JSON(map[string]interface{}{
			"user":                            userJson,
			"clinic":                          clinicJson,
			"clinicPatientNotificationConfig": clinicPatientNotificationConfigJson,
		})
	}
}

// PATCH: /users/:uid
func HandleUserUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name        string   `json:"name"`
		Username    string   `json:"username"`
		Password    string   `json:"password"`
		PhoneNumber string   `json:"phoneNumber"`
		Role        string   `json:"role"`
		IsDentist   *bool    `json:"isDentist,omitempty"`
		Percentage  *float64 `json:"percentage,omitempty"`
		HourlyRate  *float64 `json:"hourlyRate,omitempty"`
		FCMToken    *string  `json:"fcmToken,omitempty"`
	}
	return func(c *fiber.Ctx) error {
		uid := c.Params("uid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		targetUser := new(DentalClinicUser)
		if err = server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", uid, user.ClinicID).Take(targetUser).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "User not found"})
			}
			panic(err)
		}
		updateObj := map[string]interface{}{}
		if len(body.Name) != 0 {
			updateObj["name"] = body.Name
		}
		if len(body.Username) > 0 && body.Username != targetUser.Username {
			conflictUser := new(DentalClinicUser)
			if err = server.Db.Where("clinic_id = ? AND username = ?", user.ClinicID, body.Username).Take(conflictUser).Error; err != nil {
				if !errors.Is(gorm.ErrRecordNotFound, err) {
					panic(err)
				}
				updateObj["username"] = body.Username
			} else {
				return responses.Send409(c, "Another user with the same username already exists")
			}
		}
		if len(body.Password) > 0 {
			if len(body.Password) < 6 {
				return responses.Send400(c, "Password is too short")
			}
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(body.Password), 10)
			if err != nil {
				panic(err)
			}
			updateObj["password"] = hashedPassword
		}
		if len(body.PhoneNumber) > 0 {
			updateObj["phoneNumber"] = body.PhoneNumber
		}
		if len(body.Role) > 0 {
			validRole := isValidUserRole(body.Role)
			if !validRole {
				return responses.Send400(c, "Invalid role")
			}
			// check if the clinic has other master user before updating the role
			if targetUser.Role == DentalClinicRoleMaster && body.Role != string(DentalClinicRoleMaster) {
				masterUser := new(DentalClinicUser)
				if err = server.Db.Where("clinic_id = ? AND id != ? AND role = ?", user.ClinicID, targetUser.ID, string(DentalClinicRoleMaster)).Take(masterUser).Error; err != nil {
					if !errors.Is(gorm.ErrRecordNotFound, err) {
						panic(err)
					}
					return responses.Send400(c, "Cannot change the role of the last master user")
				}
			}
			updateObj["role"] = body.Role
		}
		if body.IsDentist != nil {
			updateObj["is_dentist"] = *body.IsDentist
		}
		// Only allow master user to update the percentage and hourly rate
		if body.Percentage != nil && user.Role == DentalClinicRoleMaster {
			updateObj["percentage"] = *body.Percentage
		}
		if body.HourlyRate != nil && user.Role == DentalClinicRoleMaster {
			updateObj["hourly_rate"] = *body.HourlyRate
		}
		if body.FCMToken != nil {
			updateObj["fcm_token"] = *body.FCMToken
		}
		if err = server.Db.Model(targetUser).Clauses(clause.Returning{}).UpdateColumns(updateObj).Error; err != nil {
			panic(err)
		}
		jsonUser := JsonUser{DentalClinicUser: *targetUser}
		return c.JSON(map[string]interface{}{"message": "user updated", "user": jsonUser})
	}
}

// DELETE: /users/:uid
func HandleUserDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		uid := c.Params("uid")
		targetUser := new(DentalClinicUser)
		if err := server.Db.Where("id = ? AND deleted_at IS NULL", uid).Take(targetUser).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "User not found"})
			}
			panic(err)
		}
		if targetUser.Role == DentalClinicRoleMaster {
			return responses.Send400(c, "Cannot delete master user")
		}
		if err := server.Db.Delete(targetUser).Error; err != nil {
			panic(err)
		}
		return c.Status(204).JSON(map[string]string{"message": "User deleted"})
	}
}

// PATCH: /users/me
func HandleUserUpdateSelf(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name     *string `json:"name"`
		Password *string `json:"password"`
		FCMToken *string `json:"fcmToken"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		updateObj := map[string]interface{}{}
		if body.Name != nil && len(*body.Name) != 0 {
			updateObj["name"] = body.Name
		}
		if body.Password != nil && len(*body.Password) != 0 {
			if len(*body.Password) < 6 {
				return responses.Send400(c, "Password too short")
			}
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*body.Password), 10)
			if err != nil {
				panic(err)
			}
			updateObj["password"] = hashedPassword
		}
		if body.FCMToken != nil && len(*body.FCMToken) != 0 {
			updateObj["fcm_token"] = body.FCMToken
		}
		if len(updateObj) == 0 {
			return responses.Send400(c, "Nothing to update")
		}
		if err = server.Db.Model(&user).Clauses(clause.Returning{}).UpdateColumns(updateObj).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]string{"message": "User updated"})
	}
}

// POST: /users/random-password
func HandleUserCreateRandomPassword(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		UserID string `json:"userId"`
	}

	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)

		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			panic(err)
		}

		if len(body.UserID) == 0 {
			return responses.Send400(c, "Missing user ID")
		}

		selectedUser := new(DentalClinicUser)
		if err := server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", body.UserID, user.ClinicID).Take(selectedUser).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "User not found"})
			}
			panic(err)
		}

		password, err := gonanoid.New(8)
		if err != nil {
			panic(err)
		}

		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), 10)
		if err != nil {
			panic(err)
		}

		if err := server.Db.Model(selectedUser).Clauses(clause.Returning{}).UpdateColumn("password", string(hashedPassword)).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]string{
			"message":  "Random password created successfully",
			"password": password,
		})
	}
}
