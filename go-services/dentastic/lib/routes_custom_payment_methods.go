package lib

import (
	"encoding/json"
	"errors"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type JsonCustomPaymentMethod struct {
	DentalClinicCustomPaymentMethod
	Clinic string `json:"clinic,omitempty"`
}

// GET: /custom-payment-methods
func HandleCustomPaymentMethodsList(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)

		var paymentMethods []DentalClinicCustomPaymentMethod
		if err := server.Db.Where("clinic_id = ?", user.ClinicID).Find(&paymentMethods).Error; err != nil {
			panic(err)
		}

		jsonPaymentMethods := make([]JsonCustomPaymentMethod, len(paymentMethods))
		for index, v := range paymentMethods {
			jsonPaymentMethods[index] = JsonCustomPaymentMethod{
				DentalClinicCustomPaymentMethod: v,
				Clinic:                          "",
			}
		}

		return c.JSON(map[string]interface{}{"customPaymentMethods": jsonPaymentMethods})
	}
}

// POST: /custom-payment-methods
func HandleCustomPaymentMethodCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name          string  `json:"name"`
		PercentageFee float64 `json:"percentageFee"`
		FlatFee       float64 `json:"flatFee"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)

		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		customPaymentMethod := DentalClinicCustomPaymentMethod{
			Name:          body.Name,
			PercentageFee: body.PercentageFee,
			FlatFee:       body.FlatFee,
			ClinicID:      user.ClinicID,
		}

		err = validateCustomPaymentMethod(&customPaymentMethod)
		if err != nil {
			return responses.Send400(c, err.Error())
		}

		err = validateCustomPaymentMethodNoConflict(server, &customPaymentMethod)
		if err != nil {
			return responses.Send400(c, err.Error())
		}

		if err := server.Db.Create(&customPaymentMethod).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]interface{}{"customPaymentMethod": JsonCustomPaymentMethod{DentalClinicCustomPaymentMethod: customPaymentMethod}})
	}
}

// PUT: /custom-payment-methods/:id
func HandleCustomPaymentMethodUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name          string  `json:"name"`
		PercentageFee float64 `json:"percentageFee"`
		FlatFee       float64 `json:"flatFee"`
	}
	return func(c *fiber.Ctx) error {
		id := c.Params("id")
		user := c.UserContext().Value("user").(DentalClinicUser)

		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		customPaymentMethod := DentalClinicCustomPaymentMethod{}

		if code, err := fetchCustomPaymentMethodAndVerifyOwnership(server, &customPaymentMethod, id, &user); err != nil {
			return c.Status(code).JSON(map[string]interface{}{"error": err.Error()})
		}

		customPaymentMethod.Name = body.Name
		customPaymentMethod.PercentageFee = body.PercentageFee
		customPaymentMethod.FlatFee = body.FlatFee

		err = validateCustomPaymentMethod(&customPaymentMethod)
		if err != nil {
			return responses.Send400(c, err.Error())
		}

		err = validateCustomPaymentMethodNoConflict(server, &customPaymentMethod)
		if err != nil {
			return responses.Send400(c, err.Error())
		}

		if err := server.Db.Model(&DentalClinicCustomPaymentMethod{}).Clauses(clause.Returning{}).Where("id = ?", id).Updates(&customPaymentMethod).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]interface{}{"customPaymentMethod": JsonCustomPaymentMethod{DentalClinicCustomPaymentMethod: customPaymentMethod}})
	}
}

// DELETE: /custom-payment-methods/:id
func HandleCustomPaymentMethodDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		id := c.Params("id")
		user := c.UserContext().Value("user").(DentalClinicUser)

		customPaymentMethod := DentalClinicCustomPaymentMethod{}

		if code, err := fetchCustomPaymentMethodAndVerifyOwnership(server, &customPaymentMethod, id, &user); err != nil {
			return c.Status(code).JSON(map[string]interface{}{"error": err.Error()})
		}

		if err := server.Db.Delete(&customPaymentMethod).Error; err != nil {
			panic(err)
		}

		return c.SendStatus(204)
	}
}

func validateCustomPaymentMethod(paymentMethod *DentalClinicCustomPaymentMethod) error {
	if len(paymentMethod.Name) == 0 {
		return errors.New("Name is required")
	}
	if paymentMethod.PercentageFee < 0 || paymentMethod.PercentageFee > 100 {
		return errors.New("Invalid percentage fee")
	}
	if paymentMethod.FlatFee < 0 {
		return errors.New("Invalid flat fee")
	}
	return nil
}

func validateCustomPaymentMethodNoConflict(server *config.Server, paymentMethod *DentalClinicCustomPaymentMethod) error {
	conflictPaymentMethod := DentalClinicCustomPaymentMethod{}
	if err := server.Db.Where("clinic_id = ? AND name = ?", paymentMethod.ClinicID, paymentMethod.Name).First(&conflictPaymentMethod).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			panic(err)
		}
	}
	if len(conflictPaymentMethod.ID) > 0 {
		return errors.New("Name already exists")
	}
	return nil
}

func fetchCustomPaymentMethodAndVerifyOwnership(server *config.Server, paymentMethod *DentalClinicCustomPaymentMethod, id string, user *DentalClinicUser) (int, error) {
	if err := server.Db.Where("id = ?", id).First(paymentMethod).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 404, errors.New("Custom payment method not found")
		}
		panic(err)
	}
	if paymentMethod.ClinicID != user.ClinicID {
		return 403, errors.New("Custom payment method not found")
	}
	return 0, nil
}
