package lib

import (
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"github.com/kataras/jwt"
	"github.com/stripe/stripe-go/v74"
	"gitlab.com/payrows/core/config"
	coreHelpers "gitlab.com/payrows/core/helpers"
	"gitlab.com/payrows/core/models"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

type IntializationConfig struct {
	ShouldMigrate    bool
	ExitAfterMigrate bool
	MigrateMerchant  bool
}

func Initialize(initConfig IntializationConfig) (*fiber.App, *config.Server) {
	app := fiber.New(fiber.Config{
		ErrorHandler: config.DefaultErrorHandler,
	})
	app.Use(cors.New(cors.Config{}))

	server := new(config.Server)
	server.App = app

	server.JWTAlgorithm = jwt.HS256
	server.JWTSecret = []byte(coreHelpers.EnvVarOrDefault("JWT_SECRET", "NXffNaYDQrlCu7cbo8eL1QG6azh2UWfB"))

	s3KeyId := coreHelpers.EnvVarOrDefault("S3_KEY", "DO00C6VWKUVQB9F4HMEM")
	s3SecretKey := coreHelpers.EnvVarOrDefault("S3_SECRET", "kxjYhnD4PNYuPJ+W81vkJMSTS+OT+UOsBRuxvVIeYJA")
	// s3Endpoint := coreHelpers.EnvVarOrDefault("S3_ENDPOINT", "fra1")
	s3Endpoint := coreHelpers.EnvVarOrDefault("S3_ENDPOINT", "fra1.digitaloceanspaces.com")
	// s3Region := coreHelpers.EnvVarOrDefault("S3_REGION", "payrows-dev")
	s3Region := coreHelpers.EnvVarOrDefault("S3_REGION", "fra1")
	s3Bucket := coreHelpers.EnvVarOrDefault("S3_BUCKET", "payrows-dev")
	writeSonicAPIKey := coreHelpers.EnvVarOrDefault("WRITE_SONIC_API_KEY", "fe5466d0-b33a-4207-90d8-a2a1a82996d4")
	writeSonicBotURL := coreHelpers.EnvVarOrDefault("WRITE_SONIC_BOT_URL", "https://api.writesonic.com/v1/botsonic/botsonic/generate/5709635c-ce06-4858-b532-45155442fe96")
	openAIKey := coreHelpers.EnvVarOrDefault("OPENAI_API_KEY", "********************************************************************************************************************************************************************")

	// Microservices
	server.RegisterMicroservices()

	server.S3Bucket = s3Bucket
	server.S3Endpoint = s3Endpoint
	server.S3KeyId = s3KeyId
	server.S3Region = s3Region
	server.S3SecretKey = s3SecretKey
	server.WriteSonicAPIKey = writeSonicAPIKey
	server.WriteSonicBotURL = writeSonicBotURL
	server.OpenAIKey = openAIKey

	session := session.Must(session.NewSession(&aws.Config{
		Credentials: credentials.NewStaticCredentials(s3KeyId, s3SecretKey, ""),
		Endpoint:    &s3Endpoint,
		Region:      &s3Region,
	}))
	server.S3Session = session
	S3 := s3.New(session)
	server.S3 = S3

	db, err := InitializeDB()
	if err != nil {
		log.Fatalln(err)
	}
	server.Db = db

	log.Println("Should migrate: " + strconv.FormatBool(initConfig.ShouldMigrate))

	if initConfig.MigrateMerchant {
		models.MigrateMerchant(db)
	}

	if initConfig.ShouldMigrate {
		err = db.AutoMigrate(
			// models.Merchant{},
			&DentalClinicSubscriptionPlan{},
			&DentalClinicPatientNotificationConfig{},
			&DentalClinic{},
			&DentalClinicUser{},
			&DentalClinicBranch{},
			&DentalClinicPatient{},
			&DentalClinicPatientFile{},
			&DentalClinicAppointment{},
			&DentalClinicExpense{},
			&DentalClinicInventoryItem{},
			&DentalClinicInventoryItemTransaction{},
			&DentalClinicSpecialityTemplate{},
			&DentalClinicProcedureTemplate{},
			&DentalClinicVisit{},
			&DentalClinicVisitProcedure{},
			&DentalClinicPatientPayment{},
			&DentalClinicPatientPaymentLink{},
			&DentalClinicPatientGroup{},
			&DentalClinicPatientGroupMember{},
			&DentalClinicLab{},
			&DentalClinicLabRequest{},
			&DentalClinicInsuranceCompany{},
			&DentalClinicInsuranceCompanyClaim{},
			&DentalClinicInsuranceCompanyClaimItem{},
			&DentalClinicCustomPaymentMethod{},
			&DentalClinicCommunication{},
			&DentalClinicUserOpenAILink{},
			&DentalClinicSMSQuota{},
			&DentalClinicTimesheetEntry{},
			&DentalClinicUserOTP{},
			&DentalClinicBirthdayWishConfig{},
		)
		if err != nil {
			log.Fatalln(err)
		}
	}

	if initConfig.ExitAfterMigrate {
		os.Exit(0)
	}

	port := coreHelpers.EnvVarOrDefault("PORT", "3000")
	server.Port = port

	stripeKey := coreHelpers.EnvVarOrDefault("STRIPE_KEY", "sk_test_51MNzKbGdNq0t4eg1DwHWuT3umVLhJS1JYyDhGLkZrB5qV6sevl3naZ7hfnuFLGrBp9TUSBUttcSKw7I7eYOaLiO800Z76yeZL6")
	stripe.Key = stripeKey
	server.StripeSecretKey = stripeKey

	if strings.HasPrefix(stripeKey, "sk_test_") {
		log.Println("WARNING: using test stripe key")
	}

	err = RegisterRoutes(server, app)
	if err != nil {
		log.Fatalln(err)
	}

	return app, server
}

func InitializeDB() (*gorm.DB, error) {
	// dbUrl := coreHelpers.EnvVarOrDefault("DB_URL", "postgresql://payrows:<EMAIL>:26257/defaultdb?sslmode=verify-full&options=--cluster%3Dpayrows-test-2112")
	dbUrl := coreHelpers.EnvVarOrDefault("DB_URL", "postgresql://postgres:password@localhost:5432/payrows_dentastic")
	var dialect gorm.Dialector
	if strings.HasPrefix(dbUrl, "sqlite://") {
		dialect = sqlite.Open(strings.TrimPrefix(dbUrl, "sqlite://"))
	} else {
		dialect = postgres.Open(dbUrl)
	}
	db, err := gorm.Open(dialect, &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
		Logger: gormLogger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			gormLogger.Config{
				Colorful:                  true,
				IgnoreRecordNotFoundError: true,
			},
		),
	})
	if err != nil {
		return nil, err
	}
	log.Println("Connected to " + dbUrl)
	return db, err
}

func RegisterRoutes(server *config.Server, app *fiber.App) error {

	app.Use(logger.New(
		logger.Config{
			Format: "[${time}] ${status} - ${latency} ${method} ${url}\n",
		},
	))

	app.Use(recover.New(recover.Config{
		EnableStackTrace: true,
	}))

	app.Post("/login", HandleLogin(server))
	app.Post("/register", HandleMerchantCreate(server))
	app.Post("/clinics", HandleMerchantCreate(server))
	app.Post("/forgot-password", HandleForgotPassword(server))
	app.Post("/verify-otp", HandleVerifyOTP(server))
	app.Post("/reset-password", HandleResetPassword(server))
	app.Patch("/clinics", getClinicUser(server), checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleMerchantUpdate(server))
	app.Post("/clinics/payment-methods", getClinicUser(server), HandleMerchantPaymentMethodCreate(server))
	app.Post("/clinics/sync", getClinicUser(server), HandleClinicSync(server))

	aiRoutes := app.Group("/ai")
	aiRoutes.Use(getClinicUser(server))
	aiRoutes.Get("/history", HandleAIHistory(server))
	aiRoutes.Post("/ask", HandleAIQuery(server))
	aiRoutes.Patch("/reset", HandleAIReset(server))

	usersRouter := app.Group("/users", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleExternal}))
	usersRouter.Use(checkUserSubscriptionActive(server))
	usersRouter.Get("/", HandleUsersGet(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic}))
	usersRouter.Post("/", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster, DentalClinicRoleAdmin}), HandleUserCreate(server))
	usersRouter.Get("/me", HandleUserProfileGet(server))
	usersRouter.Patch("/me", HandleUserUpdateSelf(server))
	usersRouter.Patch("/:uid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleUserUpdate(server))
	usersRouter.Delete("/:uid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleUserDelete(server))
	usersRouter.Get("/random-password", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleUserCreateRandomPassword(server))

	branchesRouter := app.Group("/branches", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	branchesRouter.Use(checkUserSubscriptionActive(server))
	branchesRouter.Get("/", HandleBranchesGet(server))
	branchesRouter.Post("/", HandleBranchesCreate(server))
	branchesRouter.Patch("/:bid", HandleBranchesUpdate(server))
	branchesRouter.Delete("/:bid", HandleBranchesDelete(server))

	patientsRouter := app.Group("/patients", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	patientsRouter.Use(checkUserSubscriptionActive(server))
	patientsRouter.Get("/", HandlePatientsList(server))
	patientsRouter.Post("/", HandlePatientCreate(server))
	patientsRouter.Get("/:pid", HandlePatientGet(server))
	patientsRouter.Patch("/:pid", HandlePatientUpdate(server))
	patientsRouter.Delete("/:pid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster, DentalClinicRoleAdmin}), HandlePatientDelete(server))
	patientsRouter.Get("/:pid/visits", HandlePatientVisitsGet(server))
	patientsRouter.Get("/:pid/files/:fid", HandlePatientFileGetOne(server))
	patientsRouter.Get("/:pid/files", HandlePatientFilesGet(server))
	patientsRouter.Post("/:pid/files", HandlePatientFileCreate(server))
	patientsRouter.Get("/:pid/claims", HandlePatientClaimsList(server))

	appointmentsRouter := app.Group("/appointments", getClinicUser(server))
	appointmentsRouter.Use(checkUserSubscriptionActive(server))
	appointmentsRouter.Get("/", HandleAppointmentsGet(server))
	appointmentsRouter.Post("/", HandleAppointmentCreate(server))
	appointmentsRouter.Post("/:aid/cancel", HandleAppointmentCancel(server))
	appointmentsRouter.Patch("/:aid/status", disallowUserRole([]DentalClinicRole{DentalClinicRoleExternal}), HandleAppointmentStatusUpdate(server))

	visitsRouter := app.Group("/visits", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	visitsRouter.Use(checkUserSubscriptionActive(server))
	visitsRouter.Get("/", HandleVisitsGet(server))
	visitsRouter.Post("/", HandleVisitCreate(server))
	visitsRouter.Get("/:vid", HandleVisitUpdate(server))
	visitsRouter.Get("/:vid/procedures", HandleVisitProceduresGet(server))
	visitsRouter.Post("/:vid/procedures", HandleVisitProcedureCreate(server))

	itemsRouter := app.Group("/items", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	itemsRouter.Use(checkUserSubscriptionActive(server))
	itemsRouter.Get("/", HandleInventoryItemsGet(server))
	itemsRouter.Post("/", HandleInventoryItemCreate(server))
	itemsRouter.Get("/transactions", HandleInventoryTransactionsGet(server))
	itemsRouter.Patch("/:iid", HandleInventoryItemUpdate(server))
	itemsRouter.Delete("/:iid", HandleInventoryItemDelete(server))
	itemsRouter.Get("/:iid/transactions", HandleInventoryItemTransactionsGet(server))
	itemsRouter.Post("/:iid/transactions", HandleInventoryItemTransactionCreate(server))

	expensesRouter := app.Group("/expenses", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	expensesRouter.Use(checkUserSubscriptionActive(server))
	expensesRouter.Get("/", HandleExpensesGet(server))
	expensesRouter.Post("/", HandleExpenseCreate(server))
	expensesRouter.Put("/:eid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleExpenseUpdate(server))

	paymentsRouter := app.Group("/payments", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	paymentsRouter.Use(checkUserSubscriptionActive(server))
	paymentsRouter.Get("/", HandlePaymentsGet(server))
	paymentsRouter.Post("/", HandlePaymentCreate(server))
	paymentsRouter.Post("/:pid/cancel", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandlePaymentCancel(server))

	patientGroupsRouter := app.Group("/patient-groups", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	patientGroupsRouter.Get("/", HandlePatientGroupsList(server))
	patientGroupsRouter.Post("/", HandlePatientGroupsCreate(server))
	patientGroupsRouter.Patch("/", HandlePatientGroupsUpdate(server))
	patientGroupsRouter.Delete("/", HandlePatientGroupsDelete(server))
	patientGroupsRouter.Get("/:id/members", HandlePatientGroupsMembersList(server))
	patientGroupsRouter.Post("/:id/members", HandlePatientGroupsMembersCreate(server))
	patientGroupsRouter.Delete("/:id/members/:memberId", HandlePatientGroupsMembersDelete(server))

	specialityTemplatesRouter := app.Group("/speciality-templates", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	specialityTemplatesRouter.Use(checkUserSubscriptionActive(server))
	specialityTemplatesRouter.Get("/", HandleSpecialityTemplatesGet(server))
	specialityTemplatesRouter.Post("/", HandleSpecialityTemplateCreate(server))
	specialityTemplatesRouter.Post("/bulk", HandleSpecialityTemplateBulkCreate(server))
	specialityTemplatesRouter.Get("/:sid", HandleSpecialityTemplateGetOne(server))
	specialityTemplatesRouter.Patch("/:sid", HandleSpecialityTemplateUpdate(server))
	specialityTemplatesRouter.Delete("/:sid", HandleSpecialityTemplateDelete(server))

	procedureTemplatesRouter := app.Group("/procedure-templates", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	procedureTemplatesRouter.Use(checkUserSubscriptionActive(server))
	procedureTemplatesRouter.Get("/", HandleProcedureTemplatesGet(server))
	procedureTemplatesRouter.Post("/", HandleProcedureTemplateCreate(server))
	procedureTemplatesRouter.Patch("/:ptid", HandleProcedureTemplateUpdate(server))
	procedureTemplatesRouter.Delete("/:ptid", HandleProcedureTemplateDelete(server))

	subscriptionPlansRouter := app.Group("/subscription-plans", disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	subscriptionPlansRouter.Get("/", HandleSubscriptionPlansGet(server))
	subscriptionPlansRouter.Post("/", getAdmin(server), HandleSubscriptionPlanCreate(server))
	subscriptionPlansRouter.Patch("/:pid", getAdmin(server), HandleSubscriptionPlanUpdate(server))

	clinicLabsRouter := app.Group("/clinic-labs", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	clinicLabsRouter.Get("/", HandleClinicLabsList(server))
	clinicLabsRouter.Post("/", HandleClinicLabCreate(server))
	clinicLabsRouter.Patch("/:lid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleClinicLabUpdate(server))
	clinicLabsRouter.Delete("/:lid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleClinicLabDelete(server))

	clinicLabRequestsRouter := app.Group("/clinic-lab-requests", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	clinicLabRequestsRouter.Get("/", HandleClinicLabRequestsList(server))
	clinicLabRequestsRouter.Post("/", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster, DentalClinicRoleAdmin}), HandleClinicLabRequestCreate(server))
	clinicLabRequestsRouter.Patch("/:rid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster, DentalClinicRoleAdmin}), HandleClinicLabRequestUpdate(server))
	clinicLabRequestsRouter.Delete("/:rid", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster, DentalClinicRoleAdmin}), HandleClinicLabRequestDelete(server))

	insuranceCompaniesRouter := app.Group("/insurance-companies", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	insuranceCompaniesRouter.Get("/", HandleInsuranceCompaniesList(server))
	insuranceCompaniesRouter.Post("/", HandleInsuranceCompanyCreate(server))
	insuranceCompaniesRouter.Patch("/:id", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleInsuranceCompanyUpdate(server))
	insuranceCompaniesRouter.Delete("/:id", checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}), HandleInsuranceCompanyDelete(server))
	insuranceCompaniesRouter.Get("/:id/claims", HandleInsuranceCompanyClaimsGet(server))
	insuranceCompaniesRouter.Post("/:id/claims", HandleInsuranceCompanyClaimsCreate(server))
	insuranceCompaniesRouter.Get("/:id/claims/:claimId", HandleInsuranceCompanyClaimsGetOne(server))
	insuranceCompaniesRouter.Put("/:id/claims/:claimId", HandleInsuranceCompanyClaimsUpdateOne(server))
	insuranceCompaniesRouter.Delete("/:id/claims/:claimId", HandleInsuranceCompanyClaimsDeleteOne(server))

	analyticsRouter := app.Group("/analytics", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	analyticsRouter.Use(checkUserSubscriptionActive(server))
	analyticsRouter.Get("/clinic", HandleClinicAnalyticsGet(server))
	analyticsRouter.Get("/dentists", HandleDentistAnalyticsGet(server), checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}))
	analyticsRouter.Get("/overdue-patients", HandleAnalyticsOverduePatientsGet(server))

	billingRouter := app.Group("/billing", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	billingRouter.Get("/current", HandleCurrentBillGet(server))

	customPaymentMethodsRouter := app.Group("/custom-payment-methods", getClinicUser(server), disallowUserRole([]DentalClinicRole{DentalClinicRoleBasic, DentalClinicRoleExternal}))
	customPaymentMethodsRouter.Get("/", HandleCustomPaymentMethodsList(server))
	customPaymentMethodsRouter.Post("/", HandleCustomPaymentMethodCreate(server))
	customPaymentMethodsRouter.Put("/:id", HandleCustomPaymentMethodUpdate(server))
	customPaymentMethodsRouter.Delete("/:id", HandleCustomPaymentMethodDelete(server))

	communicationRouter := app.Group("/communication", getClinicUser(server), checkUserRole([]DentalClinicRole{DentalClinicRoleMaster, DentalClinicRoleAdmin, DentalClinicRoleSecretary}))
	communicationRouter.Post("/sms", HandleCommunicationSendSMS(server))
	communicationRouter.Get("/birthday_wish", HandleCommunicationGetBirthdayWish(server))
	communicationRouter.Post("/birthday_wish/toggle", HandleCommunicationToggleBirthdayWish(server))

	timesheetRouter := app.Group("/timesheet", getClinicUser(server), checkUserRole([]DentalClinicRole{DentalClinicRoleMaster}))
	timesheetRouter.Post("/entries", HandleTimesheetCreate(server))
	timesheetRouter.Get("/entries", HandleTimesheetList(server))
	timesheetRouter.Patch("/entries/:id", HandleTimesheetUpdate(server))
	timesheetRouter.Delete("/entries/:id", HandleTimesheetDelete(server))

	// app.All("/*", func(c *fiber.Ctx) error {
	// 	// log.Println("Route Missing:", c.Path())
	// 	return c.Status(404).JSON(map[string]string{"error": "Page not found"})
	// })

	return nil
}
