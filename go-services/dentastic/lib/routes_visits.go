package lib

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"strconv"
	"sync"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
)

type JsonVisit struct {
	DentalClinicVisit
	Appointment string `json:"appointment,omitempty"`
	Branch      string `json:"branch,omitempty"`
	Clinic      string `json:"clinic,omitempty"`
	CreatedBy   string `json:"createdBy,omitempty"`
	Patient     string `json:"patient,omitempty"`
}

type JsonProcedure struct {
	DentalClinicVisitProcedure
	Clinic            string `json:"clinic,omitempty"`
	CreatedBy         string `json:"createdBy,omitempty"`
	ProcedureTemplate string `json:"procedureTemplate,omitempty"`
}

// GET: /visits
func HandleVisitsGet(server *config.Server) func(c *fiber.Ctx) error {
	type JsonCustomVisit struct {
		DentalClinicVisit
		Procedures []DentalClinicVisitProcedure `json:"procedures"`
		Files      []JsonPatientFile
		Clinic     string `json:"clinic,omitempty"`
		CreatedBy  string `json:"createdBy,omitempty"`
	}
	return func(c *fiber.Ctx) error {
		patientId := c.Query("patientId", "")
		pageQ := c.Query("page", "1")
		page, err := strconv.Atoi(pageQ)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid page number")
		}
		countQ := c.Query("count", "20")
		count, err := strconv.Atoi(countQ)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid count number")
		}
		user := c.UserContext().Value("user").(DentalClinicUser)
		var visits []DentalClinicVisit
		if err = server.Db.Preload("Patient").Preload("Appointment").Where("clinic_id = ? AND patient_id = ? AND deleted_at IS NULL", user.ClinicID, patientId).Order("created_at DESC").Offset((page - 1) * count).Limit(count).Find(&visits).Error; err != nil {
			panic(err)
		}
		var wg sync.WaitGroup
		jsonVisits := make([]JsonCustomVisit, len(visits))
		lookupErrorExists := false
		for index, v := range visits {
			jsonVisits[index] = JsonCustomVisit{DentalClinicVisit: v}
			wg.Add(1)
			go func(index int, visit DentalClinicVisit) {
				defer wg.Done()
				var procedures []DentalClinicVisitProcedure
				if err := server.Db.Preload("Dentist").Preload("ProcedureTemplate").Where("clinic_id = ? AND visit_id = ?", visit.ClinicID, visit.ID).Find(&procedures).Error; err != nil {
					log.Println(err)
					lookupErrorExists = true
					return
				}
				jsonVisits[index].Procedures = procedures
				var files []DentalClinicPatientFile
				if err := server.Db.Where("visit_id = ?", visit.ID).Find(&files).Error; err != nil {
					log.Println(err)
					lookupErrorExists = true
					return
				}
				jsonFiles := make([]JsonPatientFile, len(files))
				for index, v := range files {
					jsonFiles[index] = JsonPatientFile{DentalClinicPatientFile: v}
				}
				jsonVisits[index].Files = jsonFiles
			}(index, v)
		}
		wg.Wait()
		if lookupErrorExists {
			return responses.Send500(c)
		}

		return c.JSON(map[string]interface{}{"visits": jsonVisits})
	}
}

// POST: /visits
func HandleVisitCreate(server *config.Server) func(c *fiber.Ctx) error {
	type request struct {
		AppointmentID string   `json:"appointmentId"`
		BranchID      string   `json:"branchId"`
		PatientID     string   `json:"patientId"`
		Diagnosis     string   `json:"diagnosis"`
		Comments      string   `json:"comments"`
		Treatments    []string `json:"treatments"`
		NextVisit     string   `json:"nextVisit"`
		// VisitDate     string   `json:"visitDate"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		visit := new(DentalClinicVisit)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		if len(body.AppointmentID) != 0 {
			appointment := new(DentalClinicAppointment)
			if err = server.Db.Where("id = ?", body.AppointmentID).Take(appointment).Error; err != nil {
				if errors.Is(gorm.ErrRecordNotFound, err) {
					return c.Status(404).JSON(map[string]string{"error": "Appointment not found"})
				}
				panic(err)
			}
		}
		branch := new(DentalClinicBranch)
		if err = server.Db.Where("id = ? AND clinic_id = ?", body.BranchID, user.ClinicID).Take(branch).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Branch not found"})
			}
			panic(err)
		}

		patient := new(DentalClinicPatient)
		if err = server.Db.Where("id = ? AND clinic_id = ?", body.PatientID, user.ClinicID).Take(patient).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Patient not found"})
			}
			panic(err)
		}

		visit.AppointmentID = body.AppointmentID
		visit.BranchID = body.BranchID
		visit.ClinicID = user.ClinicID
		visit.CreatedByID = user.ID
		visit.PatientID = body.PatientID
		visit.Diagnosis = body.Diagnosis
		visit.Comments = body.Comments
		visit.Treatments = body.Treatments
		visit.NextVisit = body.NextVisit
		if err = server.Db.Create(visit).Error; err != nil {
			panic(err)
		}

		jsonVisit := JsonVisit{DentalClinicVisit: *visit}

		return c.Status(201).JSON(map[string]interface{}{"visit": jsonVisit})
	}
}

// PATCH: /visits/:vid
func HandleVisitUpdate(server *config.Server) func(c *fiber.Ctx) error {
	type request struct {
		Diagnosis  *string   `json:"diagnosis"`
		Comments   *string   `json:"comments"`
		Treatments *[]string `json:"treatments"`
		NextVisit  *string   `json:"nextVisit"`
	}
	return func(c *fiber.Ctx) error {
		visitId := c.Params("vid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		visit := new(DentalClinicVisit)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		if err = server.Db.Where("id = ? AND clinic_id = ?", visitId, user.ClinicID).Take(visit).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Visit not found"})
			}
			panic(err)
		}

		if visit.CreatedByID != user.ID {
			return c.Status(403).JSON(map[string]string{"error": "You are not allowed to update this visit"})
		}

		if body.Diagnosis != nil {
			visit.Diagnosis = *body.Diagnosis
		}
		if body.Comments != nil {
			visit.Comments = *body.Comments
		}
		if body.Treatments != nil {
			visit.Treatments = *body.Treatments
		}
		if body.NextVisit != nil {
			visit.NextVisit = *body.NextVisit
		}

		if err = server.Db.Save(visit).Error; err != nil {
			panic(err)
		}

		jsonVisit := JsonVisit{DentalClinicVisit: *visit}

		return c.JSON(map[string]interface{}{"visit": jsonVisit})
	}
}

// GET: /visits/:vid/procedures
func HandleVisitProceduresGet(server *config.Server) func(c *fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		visitId := c.Params("vid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		visit := new(DentalClinicVisit)
		if err := server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", visitId, user.ClinicID).Take(visit).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Visit not found"})
			}
			panic(err)
		}
		var procedures []DentalClinicVisitProcedure
		if err := server.Db.Preload("Dentist").Preload("Visit").Where("visit_id = ?", visit.ID).Find(&procedures).Error; err != nil {
			panic(err)
		}

		jsonProcedures := make([]JsonProcedure, len(procedures))

		for index, v := range procedures {
			jsonProcedures[index] = JsonProcedure{DentalClinicVisitProcedure: v}
		}

		return c.JSON(map[string]interface{}{"procedures": jsonProcedures})
	}
}

// POST: /visits/:vid/procedures
func HandleVisitProcedureCreate(server *config.Server) func(c *fiber.Ctx) error {
	type request struct {
		DentistID           string  `json:"dentistId"`
		ToothNumber         string  `json:"toothNumber"`
		Speciality          string  `json:"speciality"`
		Procedure           string  `json:"procedure"`
		NextVisit           string  `json:"nextVisit"`
		ProcedureTemplateID string  `json:"procedureTemplatId"`
		FinalPrice          float64 `json:"finalPrice"`
		ToothRemoved        bool    `json:"toothRemoved"`
		Notes               string  `json:"notes"`
	}
	return func(c *fiber.Ctx) error {
		visitId := c.Params("vid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		visit := new(DentalClinicVisit)
		if err := server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", visitId, user.ClinicID).Take(visit).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Visit not found"})
			}
			panic(err)
		}
		dentist := new(DentalClinicUser)
		if err = server.Db.Where("id = ? AND clinic_id = ?", body.DentistID, user.ClinicID).Take(dentist).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Dentist not found"})
			}
			panic(err)
		}

		procedureTemplate := new(DentalClinicProcedureTemplate)
		if err = server.Db.Where("id = ? AND clinic_id = ?", body.ProcedureTemplateID, user.ClinicID).Take(procedureTemplate).Error; err != nil {
			/* if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Procedure template not found"})
			} */
			if !errors.Is(gorm.ErrRecordNotFound, err) {
				panic(err)
			}
		}

		procedure := new(DentalClinicVisitProcedure)
		procedure.ClinicID = user.ClinicID
		procedure.CreatedByID = user.ID
		procedure.DentistID = dentist.ID
		procedure.VisitID = visit.ID
		procedure.ProcedureTemplateID = sql.NullString{String: procedureTemplate.ID, Valid: procedureTemplate.Exists()}
		procedure.Speciality = body.Speciality
		procedure.Procedure = body.Procedure
		procedure.ToothNumber = body.ToothNumber
		procedure.Price = procedureTemplate.Price
		procedure.Discount = procedure.Price - body.FinalPrice
		procedure.ToothRemoved = body.ToothRemoved
		procedure.Notes = body.Notes

		err = server.Db.Transaction(func(tx *gorm.DB) error {
			if err = tx.Create(procedure).Error; err != nil {
				panic(err)
			}
			patient := new(DentalClinicPatient)
			if err = tx.Where("id = ? AND clinic_id = ?", visit.PatientID, user.ClinicID).Take(patient).Error; err != nil {
				panic(err)
			}
			if err = tx.Model(patient).Update("balance", patient.Balance-body.FinalPrice).Error; err != nil {
				panic(err)
			}
			if procedureTemplate.Exists() && (procedureTemplate.ToothRemoved || procedureTemplate.Crown || procedureTemplate.Endo || procedureTemplate.Implant || len(procedureTemplate.Operative) != 0) {
				var updatedTeeth map[string]ToothStatus
				patientTeethStatus := patient.TeethStatus.String()
				if len(patientTeethStatus) == 0 {
					patientTeethStatus = "{}"
				}
				err = json.Unmarshal([]byte(patientTeethStatus), &updatedTeeth)
				if err != nil {
					panic(err)
				}
				status, ok := updatedTeeth[body.ToothNumber]
				if !ok {
					status = ToothStatus{}
				}
				if procedureTemplate.ToothRemoved {
					status.ToothRemoved = true
				}
				if procedureTemplate.Crown {
					status.Crown = true
				}
				if procedureTemplate.Endo {
					status.Endo = true
				}
				if procedureTemplate.Implant {
					status.Implant = true
				}
				if len(procedureTemplate.Operative) > 0 {
					status.Operative = procedureTemplate.Operative
				}
				updatedTeeth[body.ToothNumber] = status
				updatedTeethEncoded, err := json.Marshal(updatedTeeth)
				if err != nil {
					panic(err)
				}
				err = patient.TeethStatus.Scan(updatedTeethEncoded)
				if err != nil {
					panic(err)
				}
				if err = tx.Model(patient).Update("teeth_status", patient.TeethStatus).Error; err != nil {
					panic(err)
				}
			}
			return nil
		})

		if err != nil {
			panic(err)
		}

		jsonProcedure := JsonProcedure{DentalClinicVisitProcedure: *procedure}

		return c.Status(201).JSON(map[string]interface{}{"procedure": jsonProcedure})
	}
}

// GET: /visits/:vid/prescription
func HandleVisitPrescriptionGet(server *config.Server) func(c *fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		visitId := c.Params("vid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		visit := new(DentalClinicVisit)
		if err := server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", visitId, user.ClinicID).Take(visit).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Visit not found"})
			}
			panic(err)
		}
		var prescription DentalClinicVisitPrescription
		if err := server.Db.Where("visit_id = ?", visit.ID).Take(&prescription).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]interface{}{"prescription": prescription})
	}
}

// POST: /visits/:vid/prescription
func HandleVisitPrescriptionCreate(server *config.Server) func(c *fiber.Ctx) error {
	type request struct {
		Drugs []DentalClinicVisitPrescriptionDrug `json:"drugs"`
	}
	return func(c *fiber.Ctx) error {
		visitId := c.Params("vid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		visit := new(DentalClinicVisit)
		if err := server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", visitId, user.ClinicID).Take(visit).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Visit not found"})
			}
			panic(err)
		}
		var conflictPrescription DentalClinicVisitPrescription
		if err := server.Db.Where("visit_id = ?", visit.ID).Take(&conflictPrescription).Error; err != nil {
			if !errors.Is(gorm.ErrRecordNotFound, err) {
				panic(err)
			}
		}
		if conflictPrescription.Exists() {
			return c.Status(400).JSON(map[string]string{"error": "Prescription already exists"})
		}
		prescription := new(DentalClinicVisitPrescription)
		prescription.ClinicID = user.ClinicID
		prescription.CreatedByID = user.ID
		prescription.VisitID = visit.ID
		prescription.Drugs = body.Drugs

		if err = server.Db.Create(prescription).Error; err != nil {
			panic(err)
		}

		return c.Status(201).JSON(map[string]interface{}{"prescription": prescription})
	}
}

// PATCH: /visits/:vid/prescription
func HandleVisitPrescriptionUpdate(server *config.Server) func(c *fiber.Ctx) error {
	type request struct {
		Drugs []DentalClinicVisitPrescriptionDrug `json:"drugs"`
	}
	return func(c *fiber.Ctx) error {
		visitId := c.Params("vid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		visit := new(DentalClinicVisit)
		if err := server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", visitId, user.ClinicID).Take(visit).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Visit not found"})
			}
			panic(err)
		}
		var prescription DentalClinicVisitPrescription
		if err := server.Db.Where("visit_id = ?", visit.ID).Take(&prescription).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Prescription not found"})
			}
			panic(err)
		}
		prescription.Drugs = body.Drugs
		prescription.CreatedByID = user.ID

		if err = server.Db.Save(&prescription).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]interface{}{"prescription": prescription})
	}
}
