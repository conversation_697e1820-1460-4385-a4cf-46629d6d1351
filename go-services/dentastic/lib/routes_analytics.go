package lib

import (
	"log"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gorm.io/gorm"
)

func ParseFromToQuery(c *fiber.Ctx) (time.Time, time.Time) {
	now := time.Now()
	from := c.Query("from", now.Add(-1*30*24*time.Hour).Format("2006-01-02"))
	to := c.Query("to", now.Format("2006-01-02"))

	parsedFrom, err := GetDateTimeFromString(from)
	if err != nil {
		panic(err)
	}
	parsedTo, err := GetDateTimeFromString(to)
	if err != nil {
		panic(err)
	}
	startTime := time.Date(parsedFrom.Year(), parsedFrom.Month(), parsedFrom.Day(), 0, 0, 0, 0, time.UTC)
	endTime := time.Date(parsedTo.Year(), parsedTo.Month(), parsedTo.Day(), 23, 59, 59, 0, time.UTC)
	return startTime, endTime
}

// GET: /analytics/clinic
func HandleClinicAnalyticsGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		// period := c.Query("period", "month")

		startTime, endTime := ParseFromToQuery(c)

		var visitsGrouped []map[string]interface{}
		// server.Db.Raw("SELECT * FROM dental_clinic_visits WHERE clinic_id = ? AND created_at > ? AND created_at < ? GROUP BY DATE_TRUNC('day', created_at)", user.ClinicID, startTime, endTime).Scan(&visitsGrouped)
		if err := server.Db.Model(&DentalClinicVisit{}).Select("COUNT(DATE_TRUNC('day', created_at)), DATE_TRUNC('day', created_at) AS date").Where("clinic_id = ? AND created_at > ? AND created_at < ?", user.ClinicID, startTime, endTime).Group("DATE_TRUNC('day', created_at)").Scan(&visitsGrouped).Error; err != nil {
			stat := server.Db.ToSQL(func(tx *gorm.DB) *gorm.DB {
				return tx.Model(&DentalClinicVisit{}).Select("COUNT(DATE_TRUNC('day', created_at)), DATE_TRUNC('day', created_at) AS date").Where("clinic_id = ? AND created_at > ? AND created_at < ?", user.ClinicID, startTime, endTime).Group("DATE_TRUNC('day', created_at)").Scan(&visitsGrouped)
			})
			log.Println(stat)
			panic(err)
		}

		var appointmentDentistCount []map[string]interface{}
		if err := server.Db.Raw(`
			SELECT COUNT(a.dentist_id), u.id, u.name  
			FROM dental_clinic_appointments AS a 
			LEFT JOIN dental_clinic_users AS u ON a.dentist_id = u.id 
			WHERE a.clinic_id = ? AND a.start_time > ? AND a.start_time < ? 
			GROUP BY u.id
		`, user.ClinicID, startTime, endTime).Scan(&appointmentDentistCount).Error; err != nil {
			// if err := server.Db.Model(&DentalClinicAppointment{}).Select("dentist_id, COUNT(*)").Where("clinic_id = ? AND start_time > ? AND start_time < ?", user.ClinicID, startTime, endTime).Joins("LEFT JOIN dental_clinic_users as u ON u.id = dental_clinic_appointments.dentist_id").Group("dentist_id").Scan(&appointmentDentistCount).Error; err != nil {
			panic(err)
		}

		var expensesGrouped []map[string]interface{}
		if err := server.Db.Model(&DentalClinicExpense{}).Select("SUM(amount), type").Where("clinic_id = ? AND created_at > ? AND created_at < ?", user.ClinicID, startTime, endTime).Group("type").Scan(&expensesGrouped).Error; err != nil {
			stat := server.Db.ToSQL(func(tx *gorm.DB) *gorm.DB {
				return tx.Model(&DentalClinicExpense{}).Select("SUM(amount), type").Where("clinic_id = ? AND created_at > ? AND created_at < ?", user.ClinicID, startTime, endTime).Group("type").Scan(&expensesGrouped)
			})
			log.Println(stat)
			panic(err)
		}

		var newPatients map[string]interface{}
		// if err := server.Db.Model(&DentalClinicPatient{}).Where("clinic_id = ? AND created_at > ? AND created_at < ?", user.ClinicID, startTime, endTime).Joins("LEFT JOIN dental_clinic_visits AS dcv").Group("dentist_id").Scan(&appointmentDentistCount).Error; err != nil {
		if err := server.Db.Raw(`
				SELECT COUNT(DISTINCT(p.id)) FROM dental_clinic_patients as p 
				LEFT JOIN dental_clinic_visits AS v ON p.id = v.patient_id 
				WHERE p.clinic_id = ? AND p.created_at > ? AND p.created_at < ?
			`, user.ClinicID, startTime, endTime).Scan(&newPatients).Error; err != nil {
			log.Println("Error in fetching new patients")
			panic(err)
		}

		var recurringPatients map[string]interface{}
		if err := server.Db.Raw(`
				SELECT COUNT(DISTINCT(p.id)) FROM dental_clinic_patients as p 
				LEFT JOIN dental_clinic_visits AS v ON p.id = v.patient_id 
				WHERE p.clinic_id = ? AND p.created_at < ? AND v.created_at > ?
			`, user.ClinicID, startTime, startTime).Scan(&recurringPatients).Error; err != nil {
			log.Println("Error in fetching recurring patients")
			panic(err)
		}

		var proceduresTotal []map[string]interface{}
		// SELECT t.id AS procedure_id, t.procedure AS procedure_name, p.price, p.discount, v.id AS visit_id, p.tooth_number
		if err := server.Db.Raw(`
			SELECT DISTINCT(t.id) AS procedure_id, t.procedure AS procedure_name, COUNT(p.procedure_template_id), COUNT(v.id) AS teeth_operated, SUM(p.price) AS price_sum, SUM(p.discount) AS discount_sum, SUM(p.price - p.discount) AS total_sum
			FROM dental_clinic_visit_procedures AS p
			INNER JOIN dental_clinic_visits AS v ON p.visit_id = v.id
			INNER JOIN dental_clinic_procedure_templates AS t ON p.procedure_template_id = t.id
			WHERE p.clinic_id = ? AND v.created_at > ? AND v.created_at < ?
			GROUP BY t.id
			`, user.ClinicID, startTime, endTime).Scan(&proceduresTotal).Error; err != nil {
			log.Println("Error fetching procedures count")
			panic(err)
		}

		var patientReachChannel []map[string]interface{}
		if err := server.Db.Raw(`
		SELECT COUNT(DISTINCT(p.reach_channel)) AS count, p.reach_channel AS channel
		FROM dental_clinic_patients AS p
		WHERE p.clinic_id = ? AND p.created_at > ? AND p.created_at < ?
		GROUP BY p.reach_channel
		`, user.ClinicID, startTime, endTime).Scan(&patientReachChannel).Error; err != nil {
			log.Println("Error fetching patient reach channel")
			panic(err)
		}

		return c.JSON(map[string]interface{}{
			"visitsGrouped":           visitsGrouped,
			"appointmentDentistCount": appointmentDentistCount,
			"expensesGrouped":         expensesGrouped,
			"newPatients":             newPatients,
			"recurringPatients":       recurringPatients,
			"proceduresTotal":         proceduresTotal,
			"patientReachChannel":     patientReachChannel,
		})

		/* var visits interface{}
		visitsTx := server.Db.Model(&DentalClinicVisit{})
		switch period {
		case "month":
			fromDate := now.Add(-1 * 30 * 24 * time.Hour)
			visitsTx = server.Db.Where("clinic_id = ? AND created_at > ? AND created_at < ?", user.ClinicID, fromDate, now).Group("1, branch_id")
		case "year":
			fromDate := time.Date(now.Year()-1, now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			visitsTx = server.Db.Where("clinic_id = ? AND created_at > ? AND created_at < ?", user.ClinicID, fromDate, now).Group("YEAR(created_at), MONTH(created_at), branch_id")
		}
		if err := visitsTx.Find(&visits).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]interface{}{"visits": visits}) */
	}
}

// GET: /analytics/branchs

// GET: /analytics/dentists/:did

// GET: /analytics/dentists/
func HandleDentistAnalyticsGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		startTime, endTime := ParseFromToQuery(c)

		// var visitsGrouped []map[string]interface{}
		visitsGrouped, err := GetDentistsAnalytics(server, &DentistsAnalyticsParams{
			startTime: startTime,
			endTime:   endTime,
			ClinicID:  user.ClinicID,
		})
		if err != nil {
			panic(err)
		}

		return c.JSON(map[string]interface{}{"visitsGrouped": *visitsGrouped})
	}
}

// GET: /analytics/overdue-patients
func HandleAnalyticsOverduePatientsGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		patients, err := AnalyticsGetOverduePatients(server, &AnalyticsOverduePatientsParams{
			ClinicID: user.ClinicID,
		})
		if err != nil {
			panic(err)
		}
		var jsonPatients []JsonPatient
		for _, patient := range *patients {
			jsonPatients = append(jsonPatients, JsonPatient{
				DentalClinicPatient: patient,
			})
		}
		return c.JSON(map[string]interface{}{"patients": jsonPatients})
	}
}

type DentistsAnalyticsParams struct {
	startTime time.Time
	endTime   time.Time
	ClinicID  string
}

func GetDentistsAnalytics(server *config.Server, params *DentistsAnalyticsParams) (*[]map[string]interface{}, error) {
	var visitsGrouped []map[string]interface{}
	if err := server.Db.Raw(`
			SELECT a.dentist_id, COUNT(DISTINCT(v.id))
			FROM dental_clinic_visits AS v
			INNER JOIN dental_clinic_appointments AS a ON v.appointment_id = a.id
			WHERE v.clinic_id = ? AND v.created_at > ? AND v.created_at < ?
			GROUP BY a.dentist_id
		`, params.ClinicID, params.startTime, params.endTime).Scan(&visitsGrouped).Error; err != nil {
		return nil, err
	}
	return &visitsGrouped, nil
}

type AnalyticsOverduePatientsParams struct {
	ClinicID string
}

func AnalyticsGetOverduePatients(server *config.Server, params *AnalyticsOverduePatientsParams) (*[]DentalClinicPatient, error) {
	var patients []DentalClinicPatient
	if err := server.Db.Where("clinic_id = ? AND balance < 0", params.ClinicID).Find(&patients).Error; err != nil {
		return &patients, err
	}
	return &patients, nil
}
