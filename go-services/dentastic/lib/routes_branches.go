package lib

import (
	"database/sql"
	"encoding/json"
	"errors"
	"math/rand"
	"regexp"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func generateRandomColor() string {
	var letters = []rune("0123456789ABCDEF")
	rand.Seed(time.Now().UnixNano())
	zeroPoition := rand.Intn(3)
	c := []string{"", "", ""}
	c[zeroPoition] = "00"
	for index, _ := range c {
		if index == zeroPoition {
			continue
		}
		b := make([]rune, 2)
		for i := range b {
			b[i] = letters[rand.Intn(len(letters))]
		}
		c[index] = string(b)
	}
	return string(strings.Join(c, ""))
}

type JsonBranch struct {
	DentalClinicBranch
	Clinic string `json:"clinic,omitempty"`
}

// GET: /branches
func HandleBranchesGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		var branches []DentalClinicBranch
		if err := server.Db.Where("clinic_id = ? AND deleted_at IS NULL", user.ClinicID).Find(&branches).Error; err != nil {
			panic(err)
		}
		jsonBranches := make([]JsonBranch, len(branches))
		for index, v := range branches {
			jsonBranches[index] = JsonBranch{DentalClinicBranch: v}
		}
		return c.JSON(map[string]interface{}{"branches": jsonBranches})
	}
}

// POST :/branches
func HandleBranchesCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name      string  `json:"name"`
		Longitude float64 `json:"longitude"`
		Latitude  float64 `json:"latitude"`
		Color     string  `json:"color"`
		Rooms     int     `json:"rooms"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		clinic := new(DentalClinic)
		if err = server.Db.Where("id = ?", user.ClinicID).Take(clinic).Error; err != nil {
			panic(err)
		}
		var currentBranchCount int64
		if err = server.Db.Model(&DentalClinicBranch{}).Where("clinic_id = ?", user.ClinicID).Count(&currentBranchCount).Error; err != nil {
			panic(err)
		}
		// NOTE: removed branch count limit
		// if currentBranchCount >= int64(clinic.BranchCount) {
		// 	return responses.Send409(c, "You've reached the maximum number of branches that you can create")
		// }
		if len(body.Name) == 0 {
			return responses.Send400(c, "Missing branch name")
		}
		conflictBranch := new(DentalClinicBranch)
		if err = server.Db.Where("name ILIKE ? AND clinic_id = ? AND deleted_at IS NULL", body.Name, user.ClinicID).Take(conflictBranch).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			panic(err)
		}
		if len(conflictBranch.ID) != 0 {
			return responses.Send409(c, "Branch with the same name already exists")
		}
		branch := new(DentalClinicBranch)
		branch.ClinicID = user.ClinicID
		branch.Name = body.Name

		if body.Longitude != 0 && body.Latitude != 0 {
			if body.Longitude < -180 || body.Longitude > 180 {
				return responses.Send400(c, "Invalid longitude")
			}
			if body.Latitude < -90 || body.Latitude > 90 {
				return responses.Send400(c, "Invalid latitude")
			}
			branch.Longitude = sql.NullFloat64{Float64: body.Longitude, Valid: true}
			branch.Latitude = sql.NullFloat64{Float64: body.Latitude, Valid: true}
		}

		if len(body.Color) != 0 {
			matched, err := regexp.Match(`^[0-9A-Fa-f]{6}$`, []byte(body.Color))
			if err != nil {
				panic(err)
			}
			if !matched {
				return responses.Send400(c, "Invalid color")
			}
			branch.Color = body.Color
		} else {
			branch.Color = generateRandomColor()
		}

		if body.Rooms == 0 {
			branch.Rooms = 1
		} else {
			branch.Rooms = body.Rooms
		}

		if err = server.Db.Create(branch).Error; err != nil {
			panic(err)
		}
		jsonBranch := JsonBranch{DentalClinicBranch: *branch}
		return c.Status(201).JSON(map[string]interface{}{"branch": jsonBranch})
	}
}

// PATCH: /branches/:bid
func HandleBranchesUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name      string   `json:"name"`
		Longitude *float64 `json:"longitude"`
		Latitude  *float64 `json:"latitude"`
		Color     string   `json:"color"`
		Rooms     int      `json:"rooms"`
	}
	return func(c *fiber.Ctx) error {
		branchId := c.Params("bid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		updateObj := map[string]interface{}{}

		if len(body.Name) != 0 {
			conflictBranch := new(DentalClinicBranch)
			if err = server.Db.Where("(name ILIKE ? AND id != ?) AND clinic_id = ?", body.Name, branchId, user.ClinicID).Take(conflictBranch).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				panic(err)
			}
			if len(conflictBranch.ID) != 0 {
				return responses.Send409(c, "Another branch has the same name")
			}

			updateObj["name"] = body.Name
		}

		if body.Longitude != nil && body.Latitude != nil {
			if *body.Longitude < -180 || *body.Longitude > 180 {
				return responses.Send400(c, "Invalid longitude")
			}
			if *body.Latitude < -90 || *body.Latitude > 90 {
				return responses.Send400(c, "Invalid latitude")
			}
			updateObj["longitude"] = sql.NullFloat64{Float64: *body.Longitude, Valid: true}
			updateObj["latitude"] = sql.NullFloat64{Float64: *body.Latitude, Valid: true}
		} else {
			updateObj["longitude"] = sql.NullFloat64{Float64: 0, Valid: false}
			updateObj["latitude"] = sql.NullFloat64{Float64: 0, Valid: false}
		}

		if len(body.Color) != 0 {
			matched, err := regexp.Match(`^[0-9A-Fa-f]{6}$`, []byte(body.Color))
			if err != nil {
				panic(err)
			}
			if !matched {
				return responses.Send400(c, "Invalid color")
			}
			updateObj["color"] = body.Color
		}

		if body.Rooms != 0 {
			updateObj["rooms"] = body.Rooms
		}

		if len(updateObj) == 0 {
			return responses.Send400(c, "Nothing to update")
		}

		branch := new(DentalClinicBranch)
		if err = server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", branchId, user.ClinicID).Take(branch).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Branch not found"})
			}
			panic(err)
		}

		if err = server.Db.Model(branch).Clauses(clause.Returning{}).UpdateColumns(updateObj).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"branch": JsonBranch{DentalClinicBranch: *branch}})
	}
}

// DELETE: /branches/:bid
func HandleBranchesDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		return responses.Send504(c)
	}
}
