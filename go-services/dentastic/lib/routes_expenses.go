package lib

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
)

type JsonExpense struct {
	DentalClinicExpense
	Clinic string `json:"clinic,omitempty"`
}

func isValidExpenseType(expense string) bool {
	et := DentalClinicExpenseType(expense)
	switch et {
	case SalaryDentalClinicExpenseType, MaterialsDentalClinicExpenseType, FeesDentalClinicExpenseType, OthersDentalClinicExpenseType:
		return true
	}
	return false
}

// GET: /expenses
func HandleExpensesGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		page := c.Query("page", "1")
		branchID := c.Query("branchId", "")
		fromDate := c.Query("from", "")
		toDate := c.Query("to", "")

		pageNumber, err := strconv.Atoi(page)
		if err != nil {
			log.Println(c)
			return responses.Send400(c, "Invalid page")
		}

		// Build the query
		query := server.Db.Where("clinic_id = ?", user.ClinicID)

		// Filter by branch if provided
		if len(branchID) > 0 {
			branch := new(DentalClinicBranch)
			if err = server.Db.Where("id = ? AND clinic_id = ?", branchID, user.ClinicID).Take(branch).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return c.Status(404).JSON(map[string]string{"error": "Branch not found"})
				}
				panic(err)
			}
			query = query.Where("branch_id = ?", branchID)
		}

		// Filter by date range if provided
		if len(fromDate) > 0 {
			fromTime, err := GetDateTimeFromString(fromDate)
			if err != nil {
				return responses.Send400(c, "Invalid from date format. Use YYYY-MM-DD")
			}
			query = query.Where("created_at >= ?", fromTime)
		}

		if len(toDate) > 0 {
			toTime, err := GetDateTimeFromString(toDate)
			if err != nil {
				return responses.Send400(c, "Invalid to date format. Use YYYY-MM-DD")
			}
			// Set to end of day
			toTime = time.Date(toTime.Year(), toTime.Month(), toTime.Day(), 23, 59, 59, 999999999, toTime.Location())
			query = query.Where("created_at <= ?", toTime)
		}

		// Execute the query
		var expenses []DentalClinicExpense
		if err := query.Preload("Branch").Preload("CreatedBy").Order("created_at DESC").Limit(20).Offset((pageNumber - 1) * 20).Find(&expenses).Error; err != nil {
			panic(err)
		}

		jsonExpenses := make([]JsonExpense, len(expenses))
		for index, v := range expenses {
			jsonExpenses[index] = JsonExpense{DentalClinicExpense: v}
		}
		return c.JSON(map[string]interface{}{"expenses": jsonExpenses})
	}
}

// POST: /expenses
func HandleExpenseCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Amount      float64 `json:"amount"`
		Description string  `json:"description"`
		Type        string  `json:"type"`
		BranchID    string  `json:"branchId"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		// Validate branch ID if provided
		if len(body.BranchID) > 0 {
			branch := new(DentalClinicBranch)
			if err = server.Db.Where("id = ? AND clinic_id = ?", body.BranchID, user.ClinicID).Take(branch).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return c.Status(404).JSON(map[string]string{"error": "Branch not found"})
				}
				panic(err)
			}
		}

		expense := new(DentalClinicExpense)
		expense.ClinicID = user.ClinicID

		// Set branch ID if provided
		if len(body.BranchID) > 0 {
			expense.BranchID = sql.NullString{
				String: body.BranchID,
				Valid:  true,
			}
		}

		expense.CreatedByID = user.ID
		expense.Amount = body.Amount
		expense.Description = body.Description
		if !isValidExpenseType(body.Type) {
			return responses.Send400(c, "Invalid expense type")
		}
		expense.Type = DentalClinicExpenseType(body.Type)
		if err = server.Db.Create(expense).Error; err != nil {
			panic(err)
		}
		jsonExpense := JsonExpense{DentalClinicExpense: *expense}
		return c.Status(201).JSON(map[string]interface{}{"expense": jsonExpense})
	}
}

// PUT: /expenses/:eid
func HandleExpenseUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Amount      float64 `json:"amount"`
		Description string  `json:"description"`
		Type        string  `json:"type"`
	}
	return func(c *fiber.Ctx) error {
		expenseId := c.Params("eid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		expense := new(DentalClinicExpense)
		if err = server.Db.Where("id = ? AND clinic_id = ?", expenseId, user.ClinicID).Take(expense).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Expense not found"})
			}
		}
		if !isValidExpenseType(body.Type) {
			return responses.Send400(c, "Invalid expense type")
		}
		expense.Amount = body.Amount
		expense.Type = DentalClinicExpenseType(body.Type)
		expense.Description = body.Description
		if err = server.Db.Save(expense).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"expense": JsonExpense{DentalClinicExpense: *expense}})
	}
}
