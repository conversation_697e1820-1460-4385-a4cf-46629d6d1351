package lib

import (
	"database/sql"
	"errors"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/stripe/stripe-go/v74"
	"github.com/stripe/stripe-go/v74/customer"
	"github.com/stripe/stripe-go/v74/paymentmethod"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
)

type JsonCard struct {
	ID       string `json:"id"`
	Last4    string `json:"last4"`
	Brand    string `json:"brand"`
	ExpMonth int    `json:"expMonth"`
	ExpYear  int    `json:"expYear"`
}

// GET: /billing/current
func HandleCurrentBillGet(server *config.Server) func(*fiber.Ctx) error {
	const clinicNotFoundError = "Clinic not found"
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		if user.Role != DentalClinicRoleMaster {
			return c.Status(403).JSON(map[string]string{"error": "Forbidden. Only master users can access this endpoint"})
		}
		clinic := new(DentalClinic)
		if err := server.Db.Where("id = ?", user.ClinicID).First(clinic).Error; err != nil {
			if !errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": clinicNotFoundError})
			}
			panic(err)
		}
		if err := server.Db.Preload("SubscriptionPlan").Where("merchant_id = ?", clinic.MerchantID).First(clinic).Error; err != nil {
			if !errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": clinicNotFoundError})
			}
			panic(err)
		}
		visitsCount, err := getBillingVisitsCount(server, clinic)
		if err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"bill": float64(visitsCount) * 5 /* clinic.SubscriptionPlan.PricePerVisit */, "currency": "EGP", "visitsCount": visitsCount})
	}
}

// GET: /billing/cards
func HandleCardsList(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		merchant := c.UserContext().Value("merchant").(models.Merchant)
		if !merchant.StripeCustomerId.Valid {
			cid, err := CreateStripeCustomer(server, &merchant)
			if err != nil {
				return responses.Send500(c)
			}
			merchant.StripeCustomerId = sql.NullString{String: cid, Valid: true}
		}
		methods := paymentmethod.List(&stripe.PaymentMethodListParams{
			Customer: stripe.String(merchant.StripeCustomerId.String),
			Type:     stripe.String("card"),
		})
		var cards []JsonCard
		for methods.Iter.Next() {
			method := methods.Iter.Current().(*stripe.PaymentMethod)
			if method.Card != nil {
				cards = append(cards, JsonCard{
					ID:       method.ID,
					Last4:    method.Card.Last4,
					Brand:    string(method.Card.Brand),
					ExpMonth: int(method.Card.ExpMonth),
					ExpYear:  int(method.Card.ExpYear),
				})
			}
		}
		return c.JSON(cards)
	}
}

// POST: /billing/cards
func HandleCardAdd(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		PaymentMethod stripe.PaymentMethod `json:"paymentMethodId"`
	}
	return func(c *fiber.Ctx) error {
		merchant := c.UserContext().Value("merchant").(models.Merchant)
		clinic := new(DentalClinic)
		if err := server.Db.Where("merchant_id = ?", merchant.ID).First(clinic).Error; err != nil {
			if !errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Clinic not found"})
			}
			panic(err)
		}
		if !merchant.StripeCustomerId.Valid {
			cid, err := CreateStripeCustomer(server, &merchant)
			if err != nil {
				return responses.Send500(c)
			}
			merchant.StripeCustomerId = sql.NullString{String: cid, Valid: true}
		}
		body := new(request)
		if err := c.BodyParser(body); err != nil {
			panic(err)
		}
		_, err := paymentmethod.Attach(body.PaymentMethod.ID, &stripe.PaymentMethodAttachParams{
			Customer: stripe.String(merchant.StripeCustomerId.String),
			Params: stripe.Params{
				Metadata: map[string]string{
					"id":   merchant.ID,
					"type": "merchant",
				},
			},
		})
		if err != nil {
			panic(err)
		}
		if clinic.SubscriptionRenewedAt == "" {
			clinic.SubscriptionRenewedAt = time.Now().Format(time.RFC3339)
			if err := server.Db.Save(clinic).Error; err != nil {
				panic(err)
			}
		}
		return c.SendStatus(201)
	}
}

// DELETE: /billing/cards/:cid
func HandleCardDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		cardId := c.Params("cid")
		merchant := c.UserContext().Value("merchant").(models.Merchant)
		if !merchant.StripeCustomerId.Valid {
			cid, err := CreateStripeCustomer(server, &merchant)
			if err != nil {
				return responses.Send500(c)
			}
			merchant.StripeCustomerId = sql.NullString{String: cid, Valid: true}
			return c.Status(404).JSON(map[string]string{"error": "Card not found"})
		}
		methods := paymentmethod.List(&stripe.PaymentMethodListParams{
			Customer: stripe.String(merchant.StripeCustomerId.String),
		})
		found := false
		count := 0
		for methods.Next() {
			method := methods.PaymentMethod()
			count++
			if method.ID == cardId {
				found = true
			}
		}
		if !found {
			return c.Status(404).JSON(map[string]string{"error": "Card not found"})
		}
		if count < 2 {
			return c.Status(400).JSON(map[string]string{"error": "You must have at least one card"})
		}
		_, err := paymentmethod.Detach(cardId, nil)
		if err != nil {
			panic(err)
		}
		return c.SendStatus(204)
	}
}

func CreateStripeCustomer(server *config.Server, merchant *models.Merchant) (string, error) {
	c, err := customer.New(&stripe.CustomerParams{
		Name:  stripe.String(merchant.DisplayName),
		Email: stripe.String(merchant.Email),
		Phone: stripe.String(merchant.PhoneNumber),
		Params: stripe.Params{
			Metadata: map[string]string{
				"id":   merchant.ID,
				"type": "merchant",
			},
		},
	})
	if err != nil {
		return "", err
	}
	merchant.StripeCustomerId = sql.NullString{String: c.ID, Valid: true}
	if err := server.Db.Save(merchant).Error; err != nil {
		return "", err
	}
	return c.ID, nil
}

func getBillingVisitsCount(server *config.Server, clinic *DentalClinic) (int64, error) {
	query := server.Db.Model(&DentalClinicVisit{})
	// lastRenewal := clinic.SubscriptionRenewedAt
	now := time.Now()
	lastRenewal := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, time.UTC).Format(time.RFC3339)
	lastRenewal = strings.ReplaceAll(strings.ReplaceAll(lastRenewal, "T", " "), "Z", "")
	if lastRenewal == "" {
		query = query.Where("clinic_id = ?", clinic.ID)
	} else {
		query = query.Where("clinic_id = ? AND created_at >= ?", clinic.ID, lastRenewal)
	}
	var visitsCount int64
	if err := query.Count(&visitsCount).Error; err != nil {
		return 0, err
	}
	return visitsCount, nil
}
