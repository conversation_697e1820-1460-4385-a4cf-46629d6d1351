package admin

import (
	"encoding/json"
	"errors"
	"log"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/kataras/jwt"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AdminResponse struct {
	ID          string       `json:"id"`
	Email       string       `json:"email"`
	FirstName   string       `json:"firstName"`
	LastName    string       `json:"lastName"`
	Permissions []Permission `json:"permissions"`
}

// HandlerLogin handles admin login
func HandlerLogin(server *config.Server) fiber.Handler {
	type request struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}
	return func(c *fiber.Ctx) error {
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		email := strings.TrimSpace(body.Email)
		if len(email) == 0 {
			return responses.Send400(c, "Email is required")
		}

		// Find admin by email
		admin := new(Admin)
		if err = server.Db.Where("email ILIKE ? AND is_active = true", email).Take(admin).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send401(c)
			}
			log.Println("Error finding admin:", err)
			return responses.Send500(c)
		}

		// Verify password
		err = bcrypt.CompareHashAndPassword([]byte(admin.Password), []byte(body.Password))
		if err != nil {
			// Log failed login attempt
			logLoginAttempt(server, admin.ID, c.IP(), false)
			return responses.Send401(c)
		}

		// Update last login information
		now := time.Now()
		admin.LastLoginAt = &now
		admin.LastLoginIP = c.IP()
		server.Db.Model(admin).Updates(map[string]interface{}{
			"last_login_at": admin.LastLoginAt,
			"last_login_ip": admin.LastLoginIP,
		})

		// Load admin permissions
		server.Db.Preload("Permissions").Where("id = ?", admin.ID).Take(admin)

		// Generate JWT token
		tokenExpiry := time.Now().Add(24 * time.Hour) // 24 hour expiry for admin tokens
		token, err := jwt.Sign(server.JWTAlgorithm, server.JWTSecret, map[string]interface{}{
			"id":      admin.ID,
			"email":   admin.Email,
			"isAdmin": true,
			"exp":     tokenExpiry.Unix(),
		})
		if err != nil {
			log.Println("Error generating token:", err)
			return responses.Send500(c)
		}

		// Log successful login attempt
		logLoginAttempt(server, admin.ID, c.IP(), true)

		return c.JSON(map[string]interface{}{
			"token": string(token),
			"admin": AdminResponse{
				ID:          admin.ID,
				Email:       admin.Email,
				FirstName:   admin.FirstName,
				LastName:    admin.LastName,
				Permissions: admin.Permissions,
			},
		})
	}
}

func logLoginAttempt(server *config.Server, adminID string, ip string, success bool) {
	detailsJSON, err := json.Marshal(map[string]interface{}{"success": success})
	if err != nil {
		log.Println("Error marshaling login details:", err)
		return
	}

	loginHistory := AdminActionLog{
		AdminID:   adminID,
		Action:    "login",
		IP:        ip,
		Details:   string(detailsJSON),
		CreatedAt: time.Now(),
	}

	server.Db.Create(&loginHistory)
}
