package admin

import (
	"errors"
	"log"
	"strings"

	"github.com/gofiber/fiber/v2"
	"github.com/kataras/jwt"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
)

// AdminAuthMiddleware checks if the request is authenticated for admin access
func AdminAuthMiddleware(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get the Authorization header
		auth := c.Get("Authorization")
		if len(auth) == 0 || !strings.HasPrefix(auth, "Bearer ") {
			return responses.Send401(c)
		}

		// Extract the token
		tokenString := strings.TrimPrefix(auth, "Bearer ")
		if len(tokenString) == 0 {
			return responses.Send401(c)
		}

		// Verify the token
		verifiedToken, err := jwt.Verify(server.JWTAlgorithm, server.JWTSecret, []byte(tokenString))
		if err != nil {
			log.Println("JWT verification error:", err)
			return responses.Send401(c)
		}

		// Extract claims
		claims := make(map[string]interface{})
		if err = verifiedToken.Claims(&claims); err != nil {
			log.Println("Error extracting claims:", err)
			return responses.Send401(c)
		}

		// Verify this is an admin token
		isAdmin, ok := claims["isAdmin"].(bool)
		if !ok || !isAdmin {
			return responses.Send401(c)
		}

		// Get admin ID from token
		adminID, ok := claims["id"].(string)
		if !ok || len(adminID) == 0 {
			return responses.Send401(c)
		}

		// Load admin from database
		admin := new(Admin)
		if err = server.Db.Preload("Permissions").Where("id = ? AND is_active = true", adminID).Take(admin).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send401(c)
			}
			log.Println("Error finding admin:", err)
			return responses.Send500(c)
		}

		// Store admin in context
		c.Locals("admin", admin)

		return c.Next()
	}
}

func CheckPermission(permission string) fiber.Handler {
	return func(c *fiber.Ctx) error {
		admin := c.Locals("admin").(*Admin)
		if !admin.HasPermission(permission) {
			return responses.Send403(c)
		}
		return c.Next()
	}
}
