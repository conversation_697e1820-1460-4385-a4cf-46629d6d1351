package admin

import (
	"time"

	"gitlab.com/payrows/core/models"
)

type Permission struct {
	models.DbModel
	Name        string `gorm:"size:100;not null;unique"`
	Description string `gorm:"size:255"`
	Action      string `gorm:"size:100;not null"`
}

type Admin struct {
	models.DBModelWithCreatedAtUpdatedAt
	Email     string `gorm:"size:255;not null;unique"`
	Password  string `gorm:"size:255;not null"`
	FirstName string `gorm:"size:100"`
	LastName  string `gorm:"size:100"`
	IsActive  bool   `gorm:"default:true"`

	LastLoginAt *time.Time
	LastLoginIP string       `gorm:"size:45"`
	Permissions []Permission `gorm:"many2many:admin_permissions;"`
}

type AdminPermission struct {
	AdminID      string `gorm:"type:varchar;primaryKey"`
	PermissionID string `gorm:"type:varchar;primaryKey"`
	CreatedAt    time.Time
	CreatedBy    string `gorm:"size:255"`
}

type AdminActionLog struct {
	models.DBModelWithCreatedAtUpdatedAt
	AdminID   string `gorm:"type:varchar;index"`
	Admin     Admin
	Action    string `gorm:"size:100;not null"`
	EntityID  string `gorm:"size:255"`
	Details   string `gorm:"type:text"`
	IP        string `gorm:"size:45"`
	CreatedAt time.Time
}

func (a *Admin) HasPermission(permissionName string) bool {
	for _, permission := range a.Permissions {
		if permission.Name == permissionName {
			return true
		}
	}
	return false
}

func GetModels() []interface{} {
	return []interface{}{
		&Admin{},
		&Permission{},
		&AdminPermission{},
		&AdminActionLog{},
	}
}
