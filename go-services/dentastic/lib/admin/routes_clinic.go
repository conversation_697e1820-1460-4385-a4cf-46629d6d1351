package admin

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/core/responses"
	"gitlab.com/payrows/dentastic/lib"
	"gorm.io/gorm"
)

// ClinicResponse is the response format for clinics
type ClinicResponse struct {
	ID                    string `json:"id"`
	MerchantID            string `json:"merchantId"`
	DisplayName           string `json:"displayName"`
	Name                  string `json:"name"`
	Currency              string `json:"currency"`
	Country               string `json:"country"`
	BranchCount           int    `json:"branchCount"`
	UnifonicSenderId      string `json:"unifonicSenderId,omitempty"`
	SubscriptionRenewedAt string `json:"subscriptionRenewedAt"`
	SubscriptionEndDate   string `json:"subscriptionEndDate"`
	SubscriptionPlanID    string `json:"subscriptionPlanId,omitempty"`
}

// SMSQuotaResponse is the response format for SMS quota
type SMSQuotaResponse struct {
	ClinicID                  string    `json:"clinicId"`
	RemainingLocalSMS         int       `json:"remainingLocalSMS"`
	RemainingInternationalSMS int       `json:"remainingInternationalSMS"`
	RemainingWhatsApp         int       `json:"remainingWhatsApp"`
	PackageExpiryDate         time.Time `json:"packageExpiryDate"`
}

// ListClinicsHandler returns all clinics
func ListClinicsHandler(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var clinics []lib.DentalClinic
		query := server.Db.Order("id DESC")

		// Handle search query
		search := c.Query("search")
		if search != "" {
			query = query.Where("display_name ILIKE ? OR name ILIKE ?", "%"+search+"%", "%"+search+"%")
		}

		// Handle pagination
		page := c.QueryInt("page", 1)
		limit := c.QueryInt("limit", 20)
		offset := (page - 1) * limit

		var total int64
		if err := query.Model(&lib.DentalClinic{}).Count(&total).Error; err != nil {
			log.Println("Error counting clinics:", err)
			return responses.Send500(c)
		}

		if err := query.Limit(limit).Offset(offset).Find(&clinics).Error; err != nil {
			log.Println("Error fetching clinics:", err)
			return responses.Send500(c)
		}

		// Convert to response format
		response := make([]ClinicResponse, len(clinics))
		for i, clinic := range clinics {
			response[i] = ClinicResponse{
				ID:                    clinic.ID,
				MerchantID:            clinic.MerchantID,
				DisplayName:           clinic.DisplayName,
				Name:                  clinic.Name,
				Currency:              clinic.Currency,
				Country:               clinic.Country,
				BranchCount:           clinic.BranchCount,
				UnifonicSenderId:      clinic.UnifonicSenderId.String,
				SubscriptionRenewedAt: clinic.SubscriptionRenewedAt,
				SubscriptionEndDate:   clinic.SubscriptionEndDate,
				SubscriptionPlanID:    clinic.SubscriptionPlanID.String,
			}
		}

		return c.JSON(map[string]interface{}{
			"clinics": response,
			"total":   total,
			"page":    page,
			"limit":   limit,
		})
	}
}

// GetClinicHandler returns a specific clinic
func GetClinicHandler(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		clinicID := c.Params("id")
		if clinicID == "" {
			return responses.Send400(c, "Clinic ID is required")
		}

		clinic := new(lib.DentalClinic)
		if err := server.Db.Where("id = ?", clinicID).First(clinic).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send404(c)
			}
			log.Println("Error fetching clinic:", err)
			return responses.Send500(c)
		}

		// Get merchant info
		merchant := new(models.Merchant)
		if err := server.Db.Where("id = ?", clinic.MerchantID).First(merchant).Error; err != nil {
			log.Println("Error fetching merchant:", err)
			// Continue anyway, as we can still return clinic info
		}

		// Get SMS quota
		smsQuota := new(lib.DentalClinicSMSQuota)
		var smsQuotaResponse *SMSQuotaResponse
		if err := server.Db.Where("clinic_id = ?", clinicID).First(smsQuota).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Println("Error fetching SMS quota:", err)
			}
			// Continue anyway, as we can still return clinic info
		} else {
			smsQuotaResponse = &SMSQuotaResponse{
				ClinicID:                  smsQuota.ClinicID,
				RemainingLocalSMS:         smsQuota.RemainingLocalSMS,
				RemainingInternationalSMS: smsQuota.RemainingInternationalSMS,
				RemainingWhatsApp:         smsQuota.RemainingWhatsApp,
				PackageExpiryDate:         smsQuota.PackageExpiryDate,
			}
		}

		response := ClinicResponse{
			ID:                    clinic.ID,
			MerchantID:            clinic.MerchantID,
			DisplayName:           clinic.DisplayName,
			Name:                  clinic.Name,
			Currency:              clinic.Currency,
			Country:               clinic.Country,
			BranchCount:           clinic.BranchCount,
			UnifonicSenderId:      clinic.UnifonicSenderId.String,
			SubscriptionRenewedAt: clinic.SubscriptionRenewedAt,
			SubscriptionEndDate:   clinic.SubscriptionEndDate,
			SubscriptionPlanID:    clinic.SubscriptionPlanID.String,
		}

		return c.JSON(map[string]interface{}{
			"clinic":   response,
			"merchant": merchant,
			"smsQuota": smsQuotaResponse,
		})
	}
}

// CreateClinicHandler creates a new clinic
func CreateClinicHandler(server *config.Server) fiber.Handler {
	type request struct {
		DisplayName string `json:"displayName"`
		Name        string `json:"name"`
		Currency    string `json:"currency"`
		Country     string `json:"country"`
		Email       string `json:"email"`
		Password    string `json:"password"`
	}

	return func(c *fiber.Ctx) error {
		// Get the current admin from context (for logging purposes)
		_ = c.Locals("admin").(*Admin)

		// Parse request body
		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		// Validate required fields
		if strings.TrimSpace(body.DisplayName) == "" {
			return responses.Send400(c, "Display name is required")
		}
		if strings.TrimSpace(body.Name) == "" {
			return responses.Send400(c, "Name is required")
		}
		if strings.TrimSpace(body.Email) == "" {
			return responses.Send400(c, "Email is required")
		}
		if strings.TrimSpace(body.Password) == "" || len(body.Password) < 8 {
			return responses.Send400(c, "Password must be at least 8 characters")
		}

		// TODO: Implement clinic creation logic
		// This would typically involve:
		// 1. Creating a merchant
		// 2. Creating a clinic
		// 3. Creating a master user
		// 4. Setting up initial subscription

		// For now, return a not implemented response
		return c.Status(fiber.StatusNotImplemented).JSON(map[string]string{"error": "Not implemented"})
	}
}

// UpdateClinicHandler updates a clinic
func UpdateClinicHandler(server *config.Server) fiber.Handler {
	type request struct {
		DisplayName      string `json:"displayName"`
		Currency         string `json:"currency"`
		Country          string `json:"country"`
		BranchCount      int    `json:"branchCount"`
		UnifonicSenderId string `json:"unifonicSenderId"`
	}

	return func(c *fiber.Ctx) error {
		// Get the current admin from context
		currentAdmin := c.Locals("admin").(*Admin)

		// Get clinic ID from params
		clinicID := c.Params("id")
		if clinicID == "" {
			return responses.Send400(c, "Clinic ID is required")
		}

		// Parse request body
		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		// Get existing clinic
		clinic := new(lib.DentalClinic)
		if err := server.Db.Where("id = ?", clinicID).First(clinic).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send404(c)
			}
			log.Println("Error fetching clinic:", err)
			return responses.Send500(c)
		}

		// Update fields
		updates := make(map[string]interface{})
		if body.DisplayName != "" {
			updates["display_name"] = body.DisplayName
		}
		if body.Currency != "" {
			updates["currency"] = body.Currency
		}
		if body.Country != "" {
			updates["country"] = body.Country
		}
		if body.BranchCount > 0 {
			updates["branch_count"] = body.BranchCount
		}
		if body.UnifonicSenderId != "" {
			updates["unifonic_sender_id"] = sql.NullString{String: body.UnifonicSenderId, Valid: true}
		}

		// Apply updates
		if err := server.Db.Model(clinic).Updates(updates).Error; err != nil {
			log.Println("Error updating clinic:", err)
			return responses.Send500(c)
		}

		// Log the action
		logAdminAction(server, currentAdmin.ID, "update_clinic", clinicID, map[string]interface{}{
			"updates": updates,
		}, c.IP())

		// Fetch updated clinic
		if err := server.Db.Where("id = ?", clinicID).First(clinic).Error; err != nil {
			log.Println("Error fetching updated clinic:", err)
			return responses.Send500(c)
		}

		response := ClinicResponse{
			ID:                    clinic.ID,
			MerchantID:            clinic.MerchantID,
			DisplayName:           clinic.DisplayName,
			Name:                  clinic.Name,
			Currency:              clinic.Currency,
			Country:               clinic.Country,
			BranchCount:           clinic.BranchCount,
			UnifonicSenderId:      clinic.UnifonicSenderId.String,
			SubscriptionRenewedAt: clinic.SubscriptionRenewedAt,
			SubscriptionEndDate:   clinic.SubscriptionEndDate,
			SubscriptionPlanID:    clinic.SubscriptionPlanID.String,
		}

		return c.JSON(map[string]interface{}{
			"clinic": response,
		})
	}
}

// DeleteClinicHandler deletes a clinic
func DeleteClinicHandler(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get the current admin from context
		currentAdmin := c.Locals("admin").(*Admin)

		// Get clinic ID from params
		clinicID := c.Params("id")
		if clinicID == "" {
			return responses.Send400(c, "Clinic ID is required")
		}

		// Get existing clinic
		clinic := new(lib.DentalClinic)
		if err := server.Db.Where("id = ?", clinicID).First(clinic).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send404(c)
			}
			log.Println("Error fetching clinic:", err)
			return responses.Send500(c)
		}

		// Delete clinic (soft delete)
		if err := server.Db.Delete(clinic).Error; err != nil {
			log.Println("Error deleting clinic:", err)
			return responses.Send500(c)
		}

		// Log the action
		logAdminAction(server, currentAdmin.ID, "delete_clinic", clinicID, nil, c.IP())

		return c.SendStatus(fiber.StatusNoContent)
	}
}

// UpdateClinicSMSQuotaHandler updates a clinic's SMS quota
func UpdateClinicSMSQuotaHandler(server *config.Server) fiber.Handler {
	type request struct {
		RemainingLocalSMS         int       `json:"remainingLocalSMS"`
		RemainingInternationalSMS int       `json:"remainingInternationalSMS"`
		RemainingWhatsApp         int       `json:"remainingWhatsApp"`
		PackageExpiryDate         time.Time `json:"packageExpiryDate"`
	}

	return func(c *fiber.Ctx) error {
		// Get the current admin from context
		currentAdmin := c.Locals("admin").(*Admin)

		// Get clinic ID from params
		clinicID := c.Params("id")
		if clinicID == "" {
			return responses.Send400(c, "Clinic ID is required")
		}

		// Parse request body
		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		// Check if clinic exists
		clinic := new(lib.DentalClinic)
		if err := server.Db.Where("id = ?", clinicID).First(clinic).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send404(c)
			}
			log.Println("Error fetching clinic:", err)
			return responses.Send500(c)
		}

		// Get or create SMS quota
		smsQuota := new(lib.DentalClinicSMSQuota)
		if err := server.Db.Where("clinic_id = ?", clinicID).First(smsQuota).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Create new SMS quota
				smsQuota = &lib.DentalClinicSMSQuota{
					ClinicID:                  clinicID,
					RemainingLocalSMS:         body.RemainingLocalSMS,
					RemainingInternationalSMS: body.RemainingInternationalSMS,
					RemainingWhatsApp:         body.RemainingWhatsApp,
					PackageExpiryDate:         body.PackageExpiryDate,
				}
				if err := server.Db.Create(smsQuota).Error; err != nil {
					log.Println("Error creating SMS quota:", err)
					return responses.Send500(c)
				}
			} else {
				log.Println("Error fetching SMS quota:", err)
				return responses.Send500(c)
			}
		} else {
			// Update existing SMS quota
			updates := map[string]interface{}{
				"remaining_local_sms":         body.RemainingLocalSMS,
				"remaining_international_sms": body.RemainingInternationalSMS,
				"remaining_whats_app":         body.RemainingWhatsApp,
				"package_expiry_date":         body.PackageExpiryDate,
			}
			if err := server.Db.Model(smsQuota).Updates(updates).Error; err != nil {
				log.Println("Error updating SMS quota:", err)
				return responses.Send500(c)
			}
		}

		// Log the action
		logAdminAction(server, currentAdmin.ID, "update_sms_quota", clinicID, map[string]interface{}{
			"remainingLocalSMS":         body.RemainingLocalSMS,
			"remainingInternationalSMS": body.RemainingInternationalSMS,
			"remainingWhatsApp":         body.RemainingWhatsApp,
			"packageExpiryDate":         body.PackageExpiryDate,
		}, c.IP())

		response := SMSQuotaResponse{
			ClinicID:                  smsQuota.ClinicID,
			RemainingLocalSMS:         smsQuota.RemainingLocalSMS,
			RemainingInternationalSMS: smsQuota.RemainingInternationalSMS,
			RemainingWhatsApp:         smsQuota.RemainingWhatsApp,
			PackageExpiryDate:         smsQuota.PackageExpiryDate,
		}

		return c.JSON(map[string]interface{}{
			"smsQuota": response,
		})
	}
}
