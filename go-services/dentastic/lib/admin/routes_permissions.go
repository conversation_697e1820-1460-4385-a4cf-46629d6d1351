package admin

import (
	"encoding/json"
	"errors"
	"log"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// PermissionResponse is the response format for permissions
type PermissionResponse struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Action      string `json:"action"`
}

// ListPermissionsHandler returns all available permissions
func ListPermissionsHandler(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var permissions []Permission
		if err := server.Db.Find(&permissions).Error; err != nil {
			log.Println("Error fetching permissions:", err)
			return responses.Send500(c)
		}

		// Convert to response format
		response := make([]PermissionResponse, len(permissions))
		for i, perm := range permissions {
			response[i] = PermissionResponse{
				ID:          perm.ID,
				Name:        perm.Name,
				Description: perm.Description,
				Action:      perm.Action,
			}
		}

		return c.JSON(map[string]interface{}{
			"permissions": response,
		})
	}
}

// GetAdminPermissionsHandler returns permissions for a specific admin
func GetAdminPermissionsHandler(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		adminID := c.Params("id")
		if adminID == "" {
			return responses.Send400(c, "Admin ID is required")
		}

		// Get admin with permissions
		admin := new(Admin)
		if err := server.Db.Preload("Permissions").Where("id = ?", adminID).First(admin).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send404(c)
			}
			log.Println("Error fetching admin:", err)
			return responses.Send500(c)
		}

		// Convert to response format
		response := make([]PermissionResponse, len(admin.Permissions))
		for i, perm := range admin.Permissions {
			response[i] = PermissionResponse{
				ID:          perm.ID,
				Name:        perm.Name,
				Description: perm.Description,
				Action:      perm.Action,
			}
		}

		return c.JSON(map[string]interface{}{
			"permissions": response,
		})
	}
}

// UpdateAdminPermissionsHandler updates permissions for a specific admin
func UpdateAdminPermissionsHandler(server *config.Server) fiber.Handler {
	type request struct {
		PermissionIDs []string `json:"permissionIds"`
	}

	return func(c *fiber.Ctx) error {
		// Get the current admin from context
		currentAdmin := c.Locals("admin").(*Admin)

		// Get admin ID from params
		adminID := c.Params("id")
		if adminID == "" {
			return responses.Send400(c, "Admin ID is required")
		}

		// Parse request body
		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		// Start a transaction
		tx := server.Db.Begin()
		if tx.Error != nil {
			log.Println("Error starting transaction:", tx.Error)
			return responses.Send500(c)
		}

		// Get target admin
		admin := new(Admin)
		if err := tx.Where("id = ?", adminID).First(admin).Error; err != nil {
			tx.Rollback()
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send404(c)
			}
			log.Println("Error fetching admin:", err)
			return responses.Send500(c)
		}

		// Delete existing permissions
		if err := tx.Exec("DELETE FROM admin_permissions WHERE admin_id = ?", adminID).Error; err != nil {
			tx.Rollback()
			log.Println("Error deleting existing permissions:", err)
			return responses.Send500(c)
		}

		// Add new permissions
		for _, permID := range body.PermissionIDs {
			adminPerm := AdminPermission{
				AdminID:      adminID,
				PermissionID: permID,
				CreatedAt:    time.Now(),
				CreatedBy:    currentAdmin.Email,
			}
			if err := tx.Create(&adminPerm).Error; err != nil {
				tx.Rollback()
				log.Println("Error adding permission:", err)
				return responses.Send500(c)
			}
		}

		// Commit transaction
		if err := tx.Commit().Error; err != nil {
			log.Println("Error committing transaction:", err)
			return responses.Send500(c)
		}

		// Log the action
		logAdminAction(server, currentAdmin.ID, "update_permissions", adminID, map[string]interface{}{
			"permissionIds": body.PermissionIDs,
		}, c.IP())

		// Return updated permissions
		var permissions []Permission
		if err := server.Db.Joins("JOIN admin_permissions ON admin_permissions.permission_id = permissions.id").
			Where("admin_permissions.admin_id = ?", adminID).
			Find(&permissions).Error; err != nil {
			log.Println("Error fetching updated permissions:", err)
			return responses.Send500(c)
		}

		// Convert to response format
		response := make([]PermissionResponse, len(permissions))
		for i, perm := range permissions {
			response[i] = PermissionResponse{
				ID:          perm.ID,
				Name:        perm.Name,
				Description: perm.Description,
				Action:      perm.Action,
			}
		}

		return c.JSON(map[string]interface{}{
			"permissions": response,
		})
	}
}

// ListAdminsHandler returns all admins
func ListAdminsHandler(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		var admins []Admin
		if err := server.Db.Find(&admins).Error; err != nil {
			log.Println("Error fetching admins:", err)
			return responses.Send500(c)
		}

		// Convert to response format
		response := make([]AdminResponse, len(admins))
		for i, admin := range admins {
			response[i] = AdminResponse{
				ID:        admin.ID,
				Email:     admin.Email,
				FirstName: admin.FirstName,
				LastName:  admin.LastName,
			}
		}

		return c.JSON(map[string]interface{}{
			"admins": response,
		})
	}
}

// CreateAdminHandler creates a new admin
func CreateAdminHandler(server *config.Server) fiber.Handler {
	type request struct {
		Email     string   `json:"email"`
		Password  string   `json:"password"`
		FirstName string   `json:"firstName"`
		LastName  string   `json:"lastName"`
		PermIDs   []string `json:"permissionIds"`
	}

	return func(c *fiber.Ctx) error {
		// Get the current admin from context
		currentAdmin := c.Locals("admin").(*Admin)

		// Parse request body
		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		// Validate required fields
		if strings.TrimSpace(body.Email) == "" {
			return responses.Send400(c, "Email is required")
		}
		if strings.TrimSpace(body.Password) == "" || len(body.Password) < 8 {
			return responses.Send400(c, "Password must be at least 8 characters")
		}

		// Hash password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(body.Password), bcrypt.DefaultCost)
		if err != nil {
			log.Println("Error hashing password:", err)
			return responses.Send500(c)
		}

		// Create new admin
		newAdmin := Admin{
			Email:     body.Email,
			Password:  string(hashedPassword),
			FirstName: body.FirstName,
			LastName:  body.LastName,
			IsActive:  true,
		}

		// Start transaction
		tx := server.Db.Begin()
		if tx.Error != nil {
			log.Println("Error starting transaction:", tx.Error)
			return responses.Send500(c)
		}

		// Create admin
		if err := tx.Create(&newAdmin).Error; err != nil {
			tx.Rollback()
			log.Println("Error creating admin:", err)
			return responses.Send500(c)
		}

		// Add permissions if provided
		if len(body.PermIDs) > 0 {
			for _, permID := range body.PermIDs {
				adminPerm := AdminPermission{
					AdminID:      newAdmin.ID,
					PermissionID: permID,
					CreatedAt:    time.Now(),
					CreatedBy:    currentAdmin.Email,
				}
				if err := tx.Create(&adminPerm).Error; err != nil {
					tx.Rollback()
					log.Println("Error adding permission:", err)
					return responses.Send500(c)
				}
			}
		}

		// Commit transaction
		if err := tx.Commit().Error; err != nil {
			log.Println("Error committing transaction:", err)
			return responses.Send500(c)
		}

		// Log the action
		logAdminAction(server, currentAdmin.ID, "create_admin", newAdmin.ID, map[string]interface{}{
			"email": newAdmin.Email,
		}, c.IP())

		// Load admin with permissions
		if err := server.Db.Preload("Permissions").Where("id = ?", newAdmin.ID).First(&newAdmin).Error; err != nil {
			log.Println("Error loading admin with permissions:", err)
			return responses.Send500(c)
		}

		return c.Status(fiber.StatusCreated).JSON(map[string]interface{}{
			"admin": AdminResponse{
				ID:          newAdmin.ID,
				Email:       newAdmin.Email,
				FirstName:   newAdmin.FirstName,
				LastName:    newAdmin.LastName,
				Permissions: newAdmin.Permissions,
			},
		})
	}
}

// DeleteAdminHandler deletes an admin
func DeleteAdminHandler(server *config.Server) fiber.Handler {
	return func(c *fiber.Ctx) error {
		// Get the current admin from context
		currentAdmin := c.Locals("admin").(*Admin)

		// Get admin ID from params
		adminID := c.Params("id")
		if adminID == "" {
			return responses.Send400(c, "Admin ID is required")
		}

		// Check if trying to delete self
		if adminID == currentAdmin.ID {
			return responses.Send400(c, "Cannot delete yourself")
		}

		// Get admin
		admin := new(Admin)
		if err := server.Db.Where("id = ?", adminID).First(admin).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send404(c)
			}
			log.Println("Error fetching admin:", err)
			return responses.Send500(c)
		}

		// Delete admin (soft delete by setting is_active to false)
		if err := server.Db.Model(admin).Update("is_active", false).Error; err != nil {
			log.Println("Error deactivating admin:", err)
			return responses.Send500(c)
		}

		// Log the action
		logAdminAction(server, currentAdmin.ID, "delete_admin", adminID, nil, c.IP())

		return c.SendStatus(fiber.StatusNoContent)
	}
}

// Helper function to log admin actions
func logAdminAction(server *config.Server, adminID, action, entityID string, details map[string]interface{}, ip string) {
	detailsJSON, err := json.Marshal(details)
	if err != nil {
		log.Println("Error marshaling action details:", err)
		return
	}

	actionLog := AdminActionLog{
		AdminID:   adminID,
		Action:    action,
		EntityID:  entityID,
		Details:   string(detailsJSON),
		IP:        ip,
		CreatedAt: time.Now(),
	}

	server.Db.Create(&actionLog)
}
