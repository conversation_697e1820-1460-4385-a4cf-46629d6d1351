package admin

import (
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/gofiber/fiber/v2/middleware/recover"
	"gitlab.com/payrows/core/config"
)

// Initialize sets up the admin panel APIs
func Initialize(server *config.Server) error {
	// Register admin routes to the existing server
	err := RegisterRoutes(server, server.App)
	if err != nil {
		return err
	}

	// migrate admin schema
	err = server.Db.AutoMigrate(&Admin{}, &Permission{}, &AdminPermission{}, &AdminActionLog{})
	if err != nil {
		return err
	}

	err = InitPermissions(server.Db)
	if err != nil {
		return err
	}

	return nil
}

// RegisterRoutes sets up all the routes for the admin panel
func RegisterRoutes(server *config.Server, app *fiber.App) error {
	app.Use(logger.New(
		logger.Config{
			Format: "[${time}] ${status} - ${latency} ${method} ${url}\n",
		},
	))

	app.Use(recover.New(recover.Config{
		EnableStackTrace: true,
	}))

	adminRoutes := app.Group("/admin")
	adminRoutes.Post("/login", HandlerLogin(server))

	// Protected routes that require admin authentication
	protectedRoutes := adminRoutes.Group("/", AdminAuthMiddleware(server))

	// Permission routes
	permissionRoutes := protectedRoutes.Group("/permissions")
	permissionRoutes.Get("/", ListPermissionsHandler(server))

	// Admin routes
	adminUserRoutes := protectedRoutes.Group("/admins")
	adminUserRoutes.Get("/", CheckPermission("admin:list"), ListAdminsHandler(server))
	adminUserRoutes.Post("/", CheckPermission("admin:create"), CreateAdminHandler(server))
	adminUserRoutes.Get("/:id/permissions", CheckPermission("admin:read"), GetAdminPermissionsHandler(server))
	adminUserRoutes.Put("/:id/permissions", CheckPermission("admin:update"), UpdateAdminPermissionsHandler(server))
	adminUserRoutes.Delete("/:id", CheckPermission("admin:delete"), DeleteAdminHandler(server))

	// Clinic routes
	clinicRoutes := protectedRoutes.Group("/clinics")
	clinicRoutes.Get("/", CheckPermission("clinic:list"), ListClinicsHandler(server))
	clinicRoutes.Post("/", CheckPermission("clinic:create"), CreateClinicHandler(server))
	clinicRoutes.Get("/:id", CheckPermission("clinic:read"), GetClinicHandler(server))
	clinicRoutes.Put("/:id", CheckPermission("clinic:update"), UpdateClinicHandler(server))
	clinicRoutes.Delete("/:id", CheckPermission("clinic:delete"), DeleteClinicHandler(server))
	clinicRoutes.Put("/:id/sms-quota", CheckPermission("clinic:sms:edit"), UpdateClinicSMSQuotaHandler(server))

	return nil
}
