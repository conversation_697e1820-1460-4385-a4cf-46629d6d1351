package admin

import (
	"fmt"

	"gorm.io/gorm"
)

// set const permissions and add them to the database if not exists
func InitPermissions(db *gorm.DB) error {
	permissions := []Permission{
		{
			Name:        "admin:list",
			Description: "List all admins",
			Action:      "list",
		},
		{
			Name:        "admin:create",
			Description: "Create admin",
			Action:      "create",
		},
		{
			Name:        "admin:read",
			Description: "Read admin",
			Action:      "read",
		},
		{
			Name:        "admin:update",
			Description: "Update admin",
			Action:      "update",
		},
		{
			Name:        "admin:delete",
			Description: "Delete admin",
			Action:      "delete",
		},
		{
			Name:        "clinic:list",
			Description: "List all clinics",
			Action:      "list",
		},
		{
			Name:        "clinic:create",
			Description: "Create clinic",
			Action:      "create",
		},
		{
			Name:        "clinic:read",
			Description: "Read clinic",
			Action:      "read",
		},
		{
			Name:        "clinic:update",
			Description: "Update clinic",
			Action:      "update",
		},
		{
			Name:        "clinic:delete",
			Description: "Delete clinic",
			Action:      "delete",
		},
		{
			Name:        "clinic:sms:edit",
			Description: "Edit clinic SMS Quota",
			Action:      "edit",
		},
	}

	for _, permission := range permissions {
		if err := db.Where("name = ?", permission.Name).FirstOrCreate(&permission).Error; err != nil {
			return fmt.Errorf("failed to create permission: %w", err)
		}
	}

	return nil
}
