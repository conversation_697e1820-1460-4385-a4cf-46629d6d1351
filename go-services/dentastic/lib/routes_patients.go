package lib

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"mime/multipart"
	"os"
	"strings"
	"sync"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/gofiber/fiber/v2"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type JsonPatient struct {
	DentalClinicPatient
	Clinic    string `json:"clinic,omitempty"`
	CreatedBy string `json:"createdBy,omitempty"`
}

type JsonPatientWithGroups struct {
	JsonPatient
	PatientGroups []JsonPatientGroup `json:"patientGroups"`
}

type JsonPatientFile struct {
	DentalClinicPatientFile
	Clinic    string `json:"clinic,omitempty"`
	CreatedBy string `json:"createdBy,omitempty"`
	Patient   string `json:"patient,omitempty"`
	Visit     string `json:"visit,omitempty"`
}

type PopulatedVisit struct {
	JsonVisit
	Procedures []DentalClinicVisitProcedure `json:"procedures"`
}

type PopulatedPatient struct {
	JsonPatient
	Visits []PopulatedVisit `json:"visits"`
}

// GET: /patients
func HandlePatientsList(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		search := c.Query("search", "")
		groupId := c.Query("groupId", "")
		overdue := c.QueryBool("overdue", false)
		page := c.QueryInt("page", 1)
		user := c.UserContext().Value("user").(DentalClinicUser)
		var patients []DentalClinicPatient
		tx := server.Db.Where("clinic_id = ? AND deleted_at IS NULL", user.ClinicID).Session(&gorm.Session{})
		if len(search) > 0 {
			tx = tx.Where("name ILIKE ? OR phone_number LIKE ? OR email ILIKE ? OR file_number ILIKE ?", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
		}
		if overdue {
			tx = tx.Where("balance < 0")
		}
		if len(groupId) > 0 {
			var patientGroup DentalClinicPatientGroup
			if err := server.Db.Where("clinic_id = ? AND id = ?", user.ClinicID, groupId).Take(&patientGroup).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return c.JSON(map[string]interface{}{"patients": []JsonPatientWithGroups{}, "error": "Group not found"})
				}
				panic(err)
			}
			var groupMembersIds []string
			if err := server.Db.Model(&DentalClinicPatientGroupMember{}).Where("clinic_id = ? AND group_id = ?", user.ClinicID, patientGroup.ID).Pluck("patient_id", &groupMembersIds).Error; err != nil {
				panic(err)
			}
			if len(groupMembersIds) == 0 {
				return c.JSON(map[string]interface{}{"patients": []JsonPatientWithGroups{}, "error": "Group is empty"})
			}
			tx = tx.Where("id IN ?", groupMembersIds)
		}
		if err := tx.Order("created_at desc").Limit(20).Offset((page - 1) * 20).Find(&patients).Error; err != nil {
			panic(err)
		}
		jsonPatients := make([]JsonPatientWithGroups, len(patients))
		for index, v := range patients {
			jsonPatients[index] = JsonPatientWithGroups{JsonPatient: JsonPatient{DentalClinicPatient: v}}
		}
		var wg sync.WaitGroup
		for index, v := range patients {
			wg.Add(1)
			go func(v DentalClinicPatient, index int) {
				defer wg.Done()
				var patientGroups []DentalClinicPatientGroupMember
				if err := server.Db.Preload("Group").Where("clinic_id = ? AND patient_id = ?", user.ClinicID, v.ID).Find(&patientGroups).Error; err != nil {
					panic(err)
				}
				jsonPatients[index].PatientGroups = make([]JsonPatientGroup, len(patientGroups))
				for i, pg := range patientGroups {
					jsonPatients[index].PatientGroups[i] = JsonPatientGroup{DentalClinicPatientGroup: pg.Group}
				}
			}(v, index)
		}
		wg.Wait()
		return c.JSON(map[string]interface{}{"patients": jsonPatients})
	}
}

// POST: /patients
func HandlePatientCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name               string `json:"name"`
		PhoneNumber        string `json:"phoneNumber"`
		Email              string `json:"email"`
		Address            string `json:"address"`
		Birthdate          string `json:"birthdate"`
		Job                string `json:"job"`
		DentalHistory      string `json:"dentalHistory"`
		MedicalHistory     string `json:"medicalHistory"`
		TreatmentPlan      string `json:"treatmentPlan"`
		FileNumber         string `json:"fileNumber"`
		ReachChannel       string `json:"reachChannel"`
		CustomReachChannel string `json:"customReachChannel"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		patient := new(DentalClinicPatient)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		if len(body.Name) == 0 && len(body.PhoneNumber) == 0 {
			return responses.Send400(c, "No name or phone number provided")
		}
		patient.Name = body.Name
		patient.PhoneNumber = body.PhoneNumber
		patient.Email = body.Email
		patient.Address = body.Address
		if len(body.Birthdate) > 0 {
			_, err := GetDateTimeFromString(body.Birthdate)
			if err != nil {
				return responses.Send400(c, "Invalid birthdate")
			}
			patient.Birthdate = sql.NullString{
				Valid:  true,
				String: body.Birthdate,
			}
		}
		patient.Job = body.Job
		patient.DentalHistory = body.DentalHistory
		patient.MedicalHistory = body.MedicalHistory
		patient.TreatmentPlan = body.TreatmentPlan
		patient.Balance = 0

		// check file number exist
		if len(body.FileNumber) > 0 {
			existingPatient := new(DentalClinicPatient)
			if err := server.Db.Where("file_number = ? AND clinic_id = ?", body.FileNumber, user.ClinicID).Take(existingPatient).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					panic(err)
				}
			}
			if len(existingPatient.ID) > 0 {
				return responses.Send409(c, "File number already exists")
			}
		}

		patient.FileNumber = body.FileNumber
		patient.ReachChannel = body.ReachChannel
		if body.ReachChannel == "other" {
			patient.CustomReachChannel = sql.NullString{Valid: true, String: body.CustomReachChannel}
		}
		patient.ClinicID = user.ClinicID
		patient.CreatedByID = user.ID
		if err = server.Db.Create(patient).Error; err != nil {
			panic(err)
		}
		jsonPatient := JsonPatient{DentalClinicPatient: *patient}
		return c.Status(201).JSON(map[string]interface{}{"patient": jsonPatient})
	}

}

// POST: /patients/merge

// PATCH: /patients/:pid
func HandlePatientUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name               string                  `json:"name"`
		PhoneNumber        string                  `json:"phoneNumber"`
		Email              string                  `json:"email"`
		Address            string                  `json:"address"`
		Birthdate          string                  `json:"birthdate"`
		Job                string                  `json:"job"`
		DentalHistory      string                  `json:"dentalHistory"`
		MedicalHistory     string                  `json:"medicalHistory"`
		TreatmentPlan      string                  `json:"treatmentPlan"`
		TeethStatus        *map[string]ToothStatus `json:"teethStatus"`
		FileNumber         *string                 `json:"fileNumber"`
		ReachChannel       *string                 `json:"reachChannel"`
		CustomReachChannel *string                 `json:"customReachChannel"`
	}
	return func(c *fiber.Ctx) error {
		patientId := c.Params("pid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		patient := new(DentalClinicPatient)
		if err := fetchPatientAndVerifyOwnership(server, patient, patientId, &user); err != nil {
			return c.Status(404).JSON(map[string]string{"error": err.Error()})
		}
		updateObj := map[string]interface{}{}
		if len(body.Name) != 0 {
			updateObj["name"] = body.Name
		}
		if len(body.PhoneNumber) != 0 {
			updateObj["phone_number"] = body.PhoneNumber
		}
		if len(body.Email) != 0 {
			updateObj["email"] = body.Email
		}
		if len(body.Address) != 0 {
			updateObj["address"] = body.Address
		}
		if len(body.Birthdate) != 0 {
			_, err := GetDateTimeFromString(body.Birthdate)
			if err != nil {
				return responses.Send400(c, "Invalid birthdate")
			}
			updateObj["birthdate"] = body.Birthdate
		}
		if len(body.Job) != 0 {
			updateObj["job"] = body.Job
		}
		if len(body.MedicalHistory) != 0 {
			updateObj["medical_history"] = body.MedicalHistory
		}
		if len(body.DentalHistory) != 0 {
			updateObj["dental_history"] = body.DentalHistory
		}
		if len(body.TreatmentPlan) != 0 {
			updateObj["treatment_plan"] = body.TreatmentPlan
		}
		if body.TeethStatus != nil {
			updateObj["teeth_status"] = *body.TeethStatus
		}
		if body.FileNumber != nil {
			updateObj["file_number"] = *body.FileNumber
		}
		if body.ReachChannel != nil {
			updateObj["reach_channel"] = *body.ReachChannel
			if *body.ReachChannel == "other" {
				val := ""
				if body.CustomReachChannel != nil {
					val = *body.CustomReachChannel
				}
				updateObj["custom_reach_channel"] = sql.NullString{Valid: true, String: val}
			} else {
				// if the previous value was other and the new value is not other then we need to remove the custom reach channel
				if patient.ReachChannel == "other" {
					updateObj["custom_reach_channel"] = sql.NullString{Valid: false}
				}
			}

		}

		if len(updateObj) == 0 {
			return responses.Send400(c, "Nothing to update")
		}

		if err = server.Db.Model(patient).Clauses(clause.Returning{}).UpdateColumns(updateObj).Error; err != nil {
			panic(err)
		}

		jsonPatient := JsonPatient{DentalClinicPatient: *patient}

		return c.JSON(map[string]interface{}{"patient": jsonPatient})

	}
}

// DELETE: /patients/:pid
func HandlePatientDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		// TODO: see if we need to know who deleted the patient
		patientId := c.Params("pid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		patient := new(DentalClinicPatient)
		if err := fetchPatientAndVerifyOwnership(server, patient, patientId, &user); err != nil {
			return c.Status(404).JSON(map[string]string{"error": err.Error()})
		}
		if err := server.Db.Delete(patient).Error; err != nil {
			panic(err)
		}
		return c.Status(204).JSON(map[string]string{"message": "Patient deleted"})
	}
}

// GET: /patients/:pid
func HandlePatientGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		patientId := c.Params("pid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		patient := new(DentalClinicPatient)

		err := fetchPatientAndVerifyOwnership(server, patient, patientId, &user)
		if err != nil {
			return c.Status(404).JSON(map[string]string{"error": err.Error()})
		}
		jsonPatient := JsonPatient{DentalClinicPatient: *patient}

		// return c.JSON(map[string]interface{}{"patient": jsonPatient})
		var patientGroupMembership []DentalClinicPatientGroupMember
		if err := server.Db.Preload("Group").Where("patient_id = ? AND clinic_id = ?", patient.ID, user.ClinicID).Find(&patientGroupMembership).Error; err != nil {
			panic(err)
		}
		jsonPatientGroups := make([]JsonPatientGroup, len(patientGroupMembership))
		for index, v := range patientGroupMembership {
			jsonPatientGroups[index] = JsonPatientGroup{DentalClinicPatientGroup: v.Group}
		}
		jsonPatientWithGroups := JsonPatientWithGroups{JsonPatient: jsonPatient, PatientGroups: jsonPatientGroups}

		return c.JSON(map[string]interface{}{"patient": jsonPatientWithGroups})
	}
}

// GET: /patients/:pid/visits
func HandlePatientVisitsGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		patientId := c.Params("pid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		patient := new(DentalClinicPatient)
		if err := server.Db.Where("id = ? AND clinic_id = ?", patientId, user.ClinicID).Take(patient).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Patient not found or deleted"})
			}
			panic(err)
		}
		var visits []DentalClinicVisit
		if err := server.Db.Preload("Branch").Where("patient_id = ? AND clinic_id = ?", patient.ID, user.ClinicID).Find(&visits).Error; err != nil {
			panic(err)
		}

		var wg sync.WaitGroup
		errorExists := false
		populatedVisits := make([]PopulatedVisit, len(visits))
		for index, v := range visits {
			populatedVisits[index] = PopulatedVisit{JsonVisit: JsonVisit{DentalClinicVisit: v}}
			wg.Add(1)
			go func(index int, v DentalClinicVisit) {
				defer wg.Done()
				var procedures []DentalClinicVisitProcedure
				if err := server.Db.Preload("Dentist").Where("visit_id = ? AND clinic_id = ?", v.ID, user.ClinicID).Find(&procedures).Error; err != nil {
					log.Println(err)
					errorExists = true
					return
				}
				populatedVisits[index].Procedures = procedures
			}(index, v)
		}
		wg.Wait()
		if errorExists {
			return errors.New("something went wrong poplating visits")
		}

		populatedPatient := PopulatedPatient{JsonPatient: JsonPatient{DentalClinicPatient: *patient}, Visits: populatedVisits}

		return c.JSON(map[string]interface{}{"patient": populatedPatient})
	}
}

// GET: /patients/:pid/files
func HandlePatientFilesGet(server *config.Server) func(*fiber.Ctx) error {
	type CustomPatientFile struct {
		DentalClinicPatientFile
		Visit     JsonVisit `json:"visit"`
		Clinic    string    `json:"clinic,omitempty"`
		CreatedBy string    `json:"createdBy,omitempty"`
		Patient   string    `json:"patient,omitempty"`
	}
	return func(c *fiber.Ctx) error {
		patientId := c.Params("pid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		patient := new(DentalClinicPatient)
		if err := fetchPatientAndVerifyOwnership(server, patient, patientId, &user); err != nil {
			return c.Status(404).JSON(map[string]string{"error": err.Error()})
		}
		var patientFiles []DentalClinicPatientFile
		if err := server.Db.Preload("Visit").Where("patient_id = ?", patient.ID).Order("created_at DESC").Find(&patientFiles).Error; err != nil {
			panic(err)
		}
		jsonPatientFiles := make([]CustomPatientFile, len(patientFiles))
		for index, v := range patientFiles {
			jsonPatientFiles[index] = CustomPatientFile{DentalClinicPatientFile: v, Visit: JsonVisit{DentalClinicVisit: v.Visit}}
		}
		return c.JSON(map[string]interface{}{"patientFiles": jsonPatientFiles})
		// return c.JSON(map[string]interface{}{"patientFiles": patientFiles})
	}
}

// GET: /patients/:pid/files/:fid
func HandlePatientFileGetOne(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		patientId := c.Params("pid")
		fileId := c.Params("fid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		patient := new(DentalClinicPatient)
		patientFile := new(DentalClinicPatientFile)
		if err := server.Db.Where("id = ? AND clinic_id = ?", patientId, user.ClinicID).Take(patient).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Patient not found"})
			}
			panic(err)
		}
		if err := server.Db.Where("id = ? AND patient_id = ? AND clinic_id = ?", fileId, patientId, user.ClinicID).Take(patientFile).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "File not found"})
			}
			panic(err)
		}
		downloader := s3manager.NewDownloader(server.S3Session.Copy())
		file, err := os.CreateTemp("", "patient-file__*")
		if err != nil {
			panic(err)
		}

		defer os.Remove(file.Name())
		_, err = downloader.Download(file, &s3.GetObjectInput{
			Bucket: &server.S3Bucket,
			Key:    &strings.Split(patientFile.Url, strings.TrimLeft(server.S3.Endpoint, "https://")+"/")[1],
		})
		if err != nil {
			panic(err)
		}
		return c.SendFile(file.Name(), true)
	}
}

// POST: /patients/:pid/files
func HandlePatientFileCreate(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		patientId := c.Params("pid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		patient := new(DentalClinicPatient)
		if err := fetchPatientAndVerifyOwnership(server, patient, patientId, &user); err != nil {
			return c.Status(404).JSON(map[string]string{"error": err.Error()})
		}
		visitId := c.FormValue("visitId", "")
		fileVisit := new(DentalClinicVisit)
		if len(visitId) != 0 {
			if err := server.Db.Where("id = ? AND clinic_id = ?", visitId, user.ClinicID).Take(fileVisit).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return c.Status(404).JSON(map[string]string{"error": "Visit not found"})
				}
				panic(err)
			}
		}
		form, err := c.MultipartForm()
		if err != nil {
			panic(err)
		}
		fileUrls := make([]string, len(form.File["files"]))
		wg := sync.WaitGroup{}
		uploader := s3manager.NewUploader(server.S3Session.Copy())
		for index, file := range form.File["files"] {
			wg.Add(1)
			go func(index int, f *multipart.FileHeader) {
				defer wg.Done()
				fileBuffer, err := f.Open()
				if err != nil {
					log.Println(err)
					return
				}
				namePrefix, err := gonanoid.New(10)
				var acl string = s3.ObjectCannedACLPrivate
				fileKey := "patientFiles/" + patientId + "/" + namePrefix + "___" + f.Filename
				if err != nil {
					log.Println(err)
					return
				}
				output, err := uploader.Upload(&s3manager.UploadInput{
					Bucket: &server.S3Bucket,
					Body:   fileBuffer,
					Key:    &fileKey,
					ACL:    &acl,
				})
				if err != nil {
					log.Println(err)
					return
				}
				fileUrls[index] = output.Location
			}(index, file)
		}
		wg.Wait()
		allValid := true
		for _, url := range fileUrls {
			if len(url) == 0 {
				allValid = false
				break
			}
		}
		if !allValid {
			return responses.Send400(c, "Failed to process some files")
		}
		patientFiles := make([]DentalClinicPatientFile, len(fileUrls))
		server.Db.Transaction(func(tx *gorm.DB) error {
			for index, url := range fileUrls {
				patientFile := &DentalClinicPatientFile{
					ClinicID:    user.ClinicID,
					VisitID:     fileVisit.ID,
					CreatedByID: user.ID,
					PatientID:   patient.ID,
					Url:         url,
				}
				if err := tx.Create(patientFile).Error; err != nil {
					panic(err)
				}
				patientFiles[index] = *patientFile
			}
			return nil
		})
		jsonPatientFiles := make([]JsonPatientFile, len(patientFiles))
		for index, v := range patientFiles {
			jsonPatientFiles[index] = JsonPatientFile{DentalClinicPatientFile: v}
		}
		return c.Status(201).JSON(map[string]interface{}{"patientFiles": jsonPatientFiles})
	}
}

// DELETE: /patients/:pid/files/:fid

// GET: /patients/:pid/claims
func HandlePatientClaimsList(server *config.Server) func(*fiber.Ctx) error {
	type jsonDentalClinicInsuranceCompanyClaim struct {
		DentalClinicInsuranceCompanyClaim
		Clinic  string `json:"clinic;omitempty"`
		Patient string `json:"patient;omitempty"`
		Visit   string `json:"visit;omitempty"`
		// InsuranceCompany string `json:"insuranceCompany;omitempty"`
	}
	return func(c *fiber.Ctx) error {
		claims := make([]DentalClinicInsuranceCompanyClaim, 0)
		user := c.UserContext().Value("user").(DentalClinicUser)
		patientId := c.Params("pid")
		if err := server.Db.Preload("InsuranceCompany").Where("patient_id = ? AND clinic_id = ?", patientId, user.ClinicID).Find(&claims).Order("created_at DESC").Error; err != nil {
			panic(err)
		}
		jsonClaims := make([]jsonDentalClinicInsuranceCompanyClaim, len(claims))
		for index, v := range claims {
			jsonClaims[index] = jsonDentalClinicInsuranceCompanyClaim{DentalClinicInsuranceCompanyClaim: v}
		}
		return c.JSON(map[string]interface{}{"claims": jsonClaims})
	}
}

func fetchPatientAndVerifyOwnership(server *config.Server, patient *DentalClinicPatient, patientId string, user *DentalClinicUser) error {
	if err := server.Db.Where("id = ? AND clinic_id = ? AND deleted_at IS NULL", patientId, user.ClinicID).Take(patient).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("patient not found or deleted")
		}
		panic(err)
	}
	return nil
}
