package lib

import (
	"database/sql"
	"encoding/json"
	"log"
	"strconv"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
)

type TimesheetEntryWithSalary struct {
	DentalClinicTimesheetEntry
	TotalHours float64 `json:"totalHours"`
	Salary     float64 `json:"salary"`
}

// POST: /timesheet/entries
func HandleTimesheetCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		User     string `json:"user"`
		StartAt  string `json:"startAt"`
		EndAt    string `json:"EndAt"`
		BranchID string `json:"branchId"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		entry := new(DentalClinicTimesheetEntry)

		startTime, err := time.Parse(time.RFC3339, body.StartAt)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid appointment start time")
		}

		entry.StartTime = startTime
		if len(body.EndAt) > 0 {
			endTime, err := time.Parse(time.RFC3339, body.EndAt)
			if err != nil {
				log.Println(err)
				return responses.Send400(c, "Invalid appointment start time")
			}
			entry.EndTime = sql.NullTime{
				Time:  endTime,
				Valid: true,
			}
		}

		entry.CreatedByID = user.ID
		entry.UserID = body.User
		entry.ClinicID = user.ClinicID
		entry.BranchID = body.BranchID

		if err := server.Db.Save(entry).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{
			"timesheetEntry": entry,
		})
	}
}

// GET: /timesheet/entries
func HandleTimesheetList(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		// Get query parameters
		dentistId := c.Query("dentistId", "")
		startDateStr := c.Query("startDate", "")
		endDateStr := c.Query("endDate", "")
		pageStr := c.Query("page", "1")
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid page parameter")
		}

		limitStr := c.Query("limit", "10")
		limit, err := strconv.Atoi(limitStr)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid limit parameter")
		}

		// Default to current month if no dates provided
		if startDateStr == "" {
			now := time.Now()
			startOfMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
			startDateStr = startOfMonth.Format(time.RFC3339)
		}

		if endDateStr == "" {
			now := time.Now()
			endDateStr = now.Format(time.RFC3339)
		}

		// Validate pagination params
		if page < 1 {
			page = 1
		}
		if limit < 1 {
			limit = 10
		}
		offset := (page - 1) * limit

		// Parse dates
		startDate, err := time.Parse(time.RFC3339, startDateStr)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid start date format")
		}

		endDate, err := time.Parse(time.RFC3339, endDateStr)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid end date format")
		}

		// Build query
		query := server.Db.Where("start_time >= ? AND start_time <= ?", startDate, endDate)
		if dentistId != "" {
			query = query.Where("user_id = ?", dentistId)
		}

		// Get entries with pagination
		var entries []DentalClinicTimesheetEntry
		if err := query.Preload("User").Preload("Branch").Order("start_time DESC").Offset(offset).Limit(limit).Find(&entries).Error; err != nil {
			return responses.Send500(c)
		}

		// Calculate total hours and salary for each entry
		entriesWithSalary := make([]TimesheetEntryWithSalary, len(entries))
		var totalHours float64 = 0
		var totalSalary float64 = 0

		for i, entry := range entries {
			entriesWithSalary[i].DentalClinicTimesheetEntry = entry

			// Calculate hours only if EndTime is valid
			if entry.EndTime.Valid {
				hours := entry.EndTime.Time.Sub(entry.StartTime).Hours()
				if hours < 0 {
					hours = 0 // Ensure we don't have negative hours
				}
				entriesWithSalary[i].TotalHours = hours
				totalHours += hours

				// Calculate salary based on hourly rate
				salary := hours * entry.User.HourlyRate
				entriesWithSalary[i].Salary = salary
				totalSalary += salary
			} else {
				entriesWithSalary[i].TotalHours = 0
				entriesWithSalary[i].Salary = 0
			}
		}

		// Prepare response
		return c.JSON(map[string]interface{}{
			"entries": entriesWithSalary,
			"summary": map[string]interface{}{
				"totalHours":  totalHours,
				"totalSalary": totalSalary,
			},
		})
	}
}

// PATCH: /timesheet/entries/:id
func HandleTimesheetUpdate(server *config.Server) func(*fiber.Ctx) error {
	type UpdateTimesheetRequest struct {
		StartAt string `json:"startAt"`
		EndAt   string `json:"endAt"`
	}
	return func(c *fiber.Ctx) error {
		// Get entry ID from URL params
		entryID := c.Params("id")
		if entryID == "" {
			return responses.Send400(c, "Timesheet entry ID is required")
		}

		// Parse request body
		var body UpdateTimesheetRequest
		if err := json.Unmarshal(c.Body(), &body); err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid JSON format")
		}

		// Fetch existing entry
		var entry DentalClinicTimesheetEntry
		if err := server.Db.First(&entry, "id = ?", entryID).Error; err != nil {
			log.Println(err)
			return responses.Send404(c)
		}

		// Update start time if provided
		if body.StartAt != "" {
			startTime, err := time.Parse(time.RFC3339, body.StartAt)
			if err != nil {
				log.Println(err)
				return responses.Send400(c, "Invalid start time format")
			}
			entry.StartTime = startTime
		}

		// Update end time if provided
		if body.EndAt != "" {
			endTime, err := time.Parse(time.RFC3339, body.EndAt)
			if err != nil {
				log.Println(err)
				return responses.Send400(c, "Invalid end time format")
			}
			entry.EndTime = sql.NullTime{
				Time:  endTime,
				Valid: true,
			}
		}

		// Save changes to the database
		if err := server.Db.Save(&entry).Error; err != nil {
			log.Println(err)
			return responses.Send500(c)
		}

		// Return updated entry
		return c.JSON(map[string]interface{}{
			"timesheetEntry": entry,
		})
	}
}

// DELETE: /timesheet/entries/:id
func HandleTimesheetDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		// Get entry ID from URL params
		entryID := c.Params("id")
		if entryID == "" {
			return responses.Send400(c, "Timesheet entry ID is required")
		}

		// Fetch existing entry
		var entry DentalClinicTimesheetEntry
		if err := server.Db.First(&entry, "id = ?", entryID).Error; err != nil {
			log.Println(err)
			return responses.Send404(c)
		}

		// Delete the entry from the database
		if err := server.Db.Delete(&entry).Error; err != nil {
			log.Println(err)
			return responses.Send500(c)
		}

		// Return success response
		return c.JSON(map[string]interface{}{
			"success": true,
			"message": "Timesheet entry deleted successfully",
		})
	}
}
