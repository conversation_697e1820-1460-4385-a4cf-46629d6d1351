package lib

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"log"

	"github.com/gofiber/fiber/v2"
	"github.com/openai/openai-go"

	// "github.com/openai/openai-go/internal/param"
	openaiOption "github.com/openai/openai-go/option"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/helpers"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
	// "gitlab.com/payrows/dentastic/lib/openai"
)

type WriteSonicBody struct {
	Question    string   `json:"question"`
	ChatHistory []string `json:"chat_history"`
}

type AIHistoryMessage struct {
	Message string `json:"message"`
	Sender  string `json:"sender"`
	Type    string `json:"type"`
}

// GET: /ai/history
func HandleAIHistory(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		openAiLink := DentalClinicUserOpenAILink{}
		if err := server.Db.Where("user_id = ?", user.ID).First(&openAiLink).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.JSON(map[string]interface{}{"history": []AIHistoryMessage{}})
			}
			panic(err)
		}
		if !openAiLink.CurrentThread.Valid || openAiLink.CurrentThread.String == "" {
			return c.JSON(map[string]interface{}{"history": []AIHistoryMessage{}})
		}
		client := openai.NewClient(
			openaiOption.WithAPIKey(server.OpenAIKey),
		)
		msgs, err := client.Beta.Threads.Messages.List(context.Background(), openAiLink.CurrentThread.String, openai.BetaThreadMessageListParams{
			Order: openai.F(openai.BetaThreadMessageListParamsOrderDesc),
			Limit: openai.F(int64(100)),
		})
		if err != nil {
			panic(err)
		}
		var history []AIHistoryMessage
		for _, msg := range msgs.Data {
			history = append(history, AIHistoryMessage{
				Message: msg.Content[0].Text.Value,
				Sender:  string(msg.Role),
				Type:    "text",
			})
		}
		return c.JSON(map[string]interface{}{"history": history})
	}
}

// POST: /ai/ask
func HandleAIQuery(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Query   string   `json:"query"`
		History []string `json:"history"`
	}
	return func(c *fiber.Ctx) error {
		// TODO: enable counting the number of times a user asks a question and store it in the database
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		// WRITE SONIC
		/* client := &http.Client{}
		var data = strings.NewReader(fmt.Sprintf(`{"question": %q, "chat_history": %q}`, body.Query, body.History))
		req, err := http.NewRequest("POST", server.WriteSonicBotURL, data)
		if err != nil {
			return err
		}
		// req.Header.Set("Accept-Encoding", "gzip, deflate")
		req.Header.Set("Connection", "keep-alive")
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("User-Agent", "python-requests/2.28.1")
		req.Header.Set("accept", "application/json")
		req.Header.Set("token", server.WriteSonicAPIKey)
		resp, err := client.Do(req)
		if err != nil {
			return err
		}
		defer resp.Body.Close()
		bodyText, err := io.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		return c.SendString(string(bodyText)) */

		// OPENAI
		client := openai.NewClient(
			openaiOption.WithAPIKey(server.OpenAIKey),
		)
		// chat := openai.NewChatService()

		// asst_P68vCsqbRnS4Cht32ICpOKcr is the default assistant id
		assisstantId := helpers.EnvVarOrDefault("OPENAI_ASSISTANT_ID", "asst_P68vCsqbRnS4Cht32ICpOKcr")
		assisstant, err := client.Beta.Assistants.Get(context.Background(), assisstantId)
		if err != nil {
			panic(err)
		}
		// get the last thread for this user, if it doesn't exist, create a new thread
		thread, err := getUserOpenAICurrentThread(server, user.ClinicID, user.ID, client)
		if err != nil {
			panic(err)
		}
		// send the question to the thread
		_, err = client.Beta.Threads.Messages.New(context.TODO(), thread.ID, openai.BetaThreadMessageNewParams{
			Role: openai.F(openai.BetaThreadMessageNewParamsRoleUser),
			Metadata: openai.F(interface{}(map[string]interface{}{
				"userId": user.ID,
				"app":    "naab",
			})),
			Content: openai.F([]openai.MessageContentPartParamUnion{
				openai.MessageContentPartParam{
					Type: openai.F(openai.MessageContentPartParamTypeText),
					Text: openai.F(body.Query),
				},
			}),
		})
		if err != nil {
			panic(err)
		}
		run, err := client.Beta.Threads.Runs.NewAndPoll(context.TODO(), thread.ID, openai.BetaThreadRunNewParams{
			// AssistantID: openai.F(assisstantId),
			AssistantID: openai.F(assisstant.ID),
			Metadata: openai.F(interface{}(map[string]interface{}{
				"userId": user.ID,
				"app":    "naab",
			})),
		}, 500)
		if err != nil {
			panic(err)
		}
		log.Println("Thread run ", run.Status)
		if run.Status != openai.RunStatusCompleted {
			log.Println("Thread run error:", run.LastError, "\n & incomplete details:", run.IncompleteDetails)
			responses.Send500(c)
		}
		msgs, err := client.Beta.Threads.Messages.List(context.Background(), thread.ID, openai.BetaThreadMessageListParams{
			Order: openai.F(openai.BetaThreadMessageListParamsOrderDesc),
			Limit: openai.F(int64(1)),
		})
		if err != nil {
			panic(err)
		}
		lastMsg := msgs.Data[0]
		msgValue := lastMsg.Content[0].Text.Value
		return c.SendString(msgValue)
	}
}

// PATCH: /ai/reset
func HandleAIReset(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		openAiLink := DentalClinicUserOpenAILink{}
		if err := server.Db.Where("user_id = ?", user.ID).First(&openAiLink).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.SendStatus(fiber.StatusNotFound)
			}
			panic(err)
		}
		openAiLink.CurrentThread = sql.NullString{String: "", Valid: false}
		if err := server.Db.Save(&openAiLink).Error; err != nil {
			panic(err)
		}
		return c.SendStatus(fiber.StatusOK)
	}
}

/*
	We are going to create 3 functions.
	- One will be to fetch messages of a thread.
	- One of them will handle when a user asks a question.
		The function will check if there is a thread id linked to the user,
		if the thread id exists, we will assign this thread id to the variable threadId.
		if it doesn't exist, we will create a new thread id and assign it to the threadId variable.
		We will then send the question to the OpenAi API to that thread id.
	- the last function will be reset a user's convesation.
		that means we will keep the thread assigned, but we will clear the currently linked thread id.

	This structure will also allow us to understand how many times a user asks a question and the token usage.
*/

// type OpenAIBody struct {
// 	Message string `json:"message"`
// }

// Gets the current thread history of a user. If there is none, it will create a thread and return an empty array
// TODO: replace the interface{} with a struct
func getUserOpenAICurrentThread(server *config.Server, clinicId string, userId string, client *openai.Client) (*openai.Thread, error) {
	openAiLink := DentalClinicUserOpenAILink{}
	if err := server.Db.Where("user_id = ?", userId).First(&openAiLink).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			client.Beta.Threads.New(context.TODO(), openai.BetaThreadNewParams{})
			thread, err := client.Beta.Threads.New(context.TODO(), openai.BetaThreadNewParams{})
			if err != nil {
				return nil, err
			}
			openAiLink = DentalClinicUserOpenAILink{
				ClinicID:      clinicId,
				UserID:        userId,
				CurrentThread: sql.NullString{String: thread.ID, Valid: true},
				ThreadHistory: []string{thread.ID},
			}
			server.Db.Create(&openAiLink)
			return thread, nil
		}
		panic(err)
	} else {
		if openAiLink.CurrentThread.Valid {
			thread, err := client.Beta.Threads.Get(context.Background(), openAiLink.CurrentThread.String)
			if err != nil {
				return nil, err
			}
			return thread, nil
		}
		thread, err := client.Beta.Threads.New(context.TODO(), openai.BetaThreadNewParams{})
		if err != nil {
			return nil, err
		}
		openAiLink.CurrentThread = sql.NullString{String: thread.ID, Valid: true}
		openAiLink.ThreadHistory = append(openAiLink.ThreadHistory, thread.ID)
		if err = server.Db.Save(&openAiLink).Error; err != nil {
			return nil, err
		}
		return thread, nil
	}
}
