package lib

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
)

type JsonAppointment struct {
	DentalClinicAppointment
	Branch    string `json:"branch,omitempty"`
	Clinic    string `json:"clinic,omitempty"`
	CreatedBy string `json:"createdBy,omitempty"`
	Patient   string `json:"patient,omitempty"`
	Dentist   string `json:"dentist,omitempty"`
}

type PopulatedAppointment struct {
	JsonAppointment
	Branch      JsonBranch  `json:"branch"`
	Patient     JsonPatient `json:"patient"`
	Dentist     JsonUser    `json:"dentist"`
	Visit       *JsonVisit  `json:"visit"`
	CancelledBy string      `json:"cancelledBy,omitempty"`
}

func GetDateTimeFromString(dateTime string) (time.Time, error) {
	return time.Parse("2006-01-02", dateTime)
}

func FormatDateToString(dateTime time.Time) string {
	return dateTime.Format("2006-01-02")
}

// GET: /appointments
func HandleAppointmentsGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		dentistId := c.Query("dentistId", "")
		branchId := c.Query("branchId", "")
		patientId := c.Query("patientId", "")
		from := c.Query("from", FormatDateToString(time.Now()))
		fromTime, err := GetDateTimeFromString(from)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid 'from' time")
		}
		to := c.Query("to", FormatDateToString(time.Now().Add(24*time.Hour)))
		toTime, err := GetDateTimeFromString(to)
		toTime = time.Date(toTime.Year(), toTime.Month(), toTime.Day(), 23, 59, 59, 59, toTime.Location())
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid 'to' time")
		}
		if fromTime.After(toTime) {
			return responses.Send400(c, "'From' time can't be after 'To' time")
		}
		query := server.Db.Preload("Branch").Preload("Patient").Preload("Dentist").Where("clinic_id = ? AND start_time > ? AND start_time < ?", user.ClinicID, fromTime, toTime)
		if len(dentistId) > 0 {
			dentist := new(DentalClinicUser)
			if err = server.Db.Where("id = ? AND clinic_id = ? AND is_dentist = TRUE", dentistId, user.ClinicID).Take(dentist).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return c.Status(404).JSON(map[string]string{"error": "Dentist not found"})
				}
				panic(err)
			}
			query = query.Where("dentist_id = ?", dentist.ID)
		}
		if len(branchId) > 0 {
			branch := new(DentalClinicBranch)
			if err = server.Db.Where("id = ? AND clinic_id = ?", branchId, user.ClinicID).Take(branch).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return c.Status(404).JSON(map[string]string{"error": "Branch not found"})
				}
				panic(err)
			}
			query = query.Where("branch_id = ?", branch.ID)
		}
		if len(patientId) > 0 {
			patient := new(DentalClinicPatient)
			if err = server.Db.Where("id = ? AND clinic_id = ?", patientId, user.ClinicID).Take(patient).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return c.Status(404).JSON(map[string]string{"error": "Patient not found"})
				}
				panic(err)
			}
			query = query.Where("patient_id = ?", patientId)
		}
		var appointments []DentalClinicAppointment
		if err = query.Find(&appointments).Error; err != nil {
			panic(err)
		}

		wg := sync.WaitGroup{}
		populatedAppointments := make([]PopulatedAppointment, len(appointments))
		for index, v := range appointments {
			populatedAppointments[index] = PopulatedAppointment{
				JsonAppointment: JsonAppointment{DentalClinicAppointment: v},
				Patient:         JsonPatient{DentalClinicPatient: v.Patient},
				Branch:          JsonBranch{DentalClinicBranch: v.Branch},
				Dentist:         JsonUser{DentalClinicUser: v.Dentist},
			}
			wg.Add(1)
			go func(index int, appointmentId string) {
				defer wg.Done()
				associatedVisit := new(DentalClinicVisit)
				if err := server.Db.Where("appointment_id = ?", appointmentId).Take(associatedVisit).Error; err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						populatedAppointments[index].Visit = nil
						return
					}
					panic(err)
				}
				populatedAppointments[index].Visit = &JsonVisit{DentalClinicVisit: *associatedVisit}
			}(index, v.ID)
		}
		wg.Wait()

		// return c.JSON(map[string]interface{}{"appointments": appointments})
		return c.JSON(map[string]interface{}{"appointments": populatedAppointments})
		/*
			jsonAppointments := make([]JsonAppointment, len(appointments))

			for index, v := range appointments {
				jsonAppointments[index] = JsonAppointment{DentalClinicAppointment: v}
			}

			return c.JSON(map[string]interface{}{"appointments": jsonAppointments})
		*/
	}
}

// POST: /appointments
func HandleAppointmentCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		PatientID string `json:"patientId"`
		StartTime string `json:"startTime"`
		EndTime   string `json:"endTime"`
		BranchID  string `json:"branchId"`
		DentistID string `json:"dentistId"`
		Room      int    `json:"room"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		appointment := new(DentalClinicAppointment)
		patient := new(DentalClinicPatient)
		if err = server.Db.Where("id = ? AND clinic_id = ?", body.PatientID, user.ClinicID).Take(patient).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Patient not found"})
			}
			panic(err)
		}
		branch := new(DentalClinicBranch)
		if err = server.Db.Where("id = ? AND clinic_id = ?", body.BranchID, user.ClinicID).Take(branch).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Branch not found"})
			}
			panic(err)
		}
		if len(body.DentistID) == 0 {
			return responses.Send400(c, "Missing denstist")
		}
		dentist := new(DentalClinicUser)
		if err = server.Db.Where("id = ? AND clinic_id = ?", body.DentistID, user.ClinicID).Take(dentist).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Dentist user not found"})
			}
			panic(err)
		}
		// startTime, err := GetDateTimeFromString(body.Datetime)
		startTime, err := time.Parse(time.RFC3339, body.StartTime)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid appointment start time")
		}
		endTime, err := time.Parse(time.RFC3339, body.EndTime)
		if err != nil {
			log.Println(err)
			return responses.Send400(c, "Invalid appointment end time")
		}
		if endTime.Before(startTime) || endTime.Equal(startTime) {
			return responses.Send400(c, "End time cannot be before start time")
		}
		appointment.BranchID = branch.ID
		appointment.ClinicID = user.ClinicID
		appointment.CreatedByID = user.ID
		appointment.StartTime = startTime
		appointment.EndTime = endTime
		appointment.DentistID = sql.NullString{
			String: dentist.ID,
			Valid:  true,
		}
		appointment.PatientID = patient.ID
		if body.Room == 0 {
			appointment.Room = 1
		} else {
			appointment.Room = body.Room
		}
		if err = server.Db.Create(appointment).Error; err != nil {
			panic(err)
		}
		jsonAppointment := PopulatedAppointment{
			JsonAppointment: JsonAppointment{DentalClinicAppointment: *appointment},
			Branch:          JsonBranch{DentalClinicBranch: *branch},
			Patient:         JsonPatient{DentalClinicPatient: *patient},
			Dentist:         JsonUser{DentalClinicUser: *dentist},
		}
		return c.Status(201).JSON(map[string]interface{}{"appointment": jsonAppointment})
	}
}

// PATCH: /appointments/:aid/cancel
func HandleAppointmentCancel(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		appointmentId := c.Params("aid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		appointment := new(DentalClinicAppointment)
		if err := server.Db.Where("id = ? AND clinic_id = ?", appointmentId, user.ClinicID).Take(appointment).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Appointment not found"})
			}
			panic(err)
		}
		if appointment.CancelledAt.Valid {
			return responses.Send409(c, "Appointment already cancelled")
		}
		appointment.CancelledByID = sql.NullString{
			Valid:  true,
			String: user.ID,
		}
		appointment.CancelledAt = sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		}
		appointment.Status = AppointmentStatusCancelled
		if err := server.Db.Save(appointment).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"appointment": *appointment})
	}
}

// PATCH: /appointments/:aid/status
func HandleAppointmentStatusUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Status DentalClinicAppointmentStatus `json:"status"`
	}
	return func(c *fiber.Ctx) error {
		appointmentId := c.Params("aid")
		user := c.UserContext().Value("user").(DentalClinicUser)

		appointment := new(DentalClinicAppointment)
		if err := server.Db.Where("id = ? AND clinic_id = ?", appointmentId, user.ClinicID).Take(appointment).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Appointment not found"})
			}
			panic(err)
		}

		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			return responses.Send400(c, "Invalid request body")
		}

		// Validate status
		switch body.Status {
		case AppointmentStatusPending, AppointmentStatusConfirmed, AppointmentStatusCancelled, AppointmentStatusRescheduled:
			// Valid status
		default:
			return responses.Send400(c, "Invalid appointment status")
		}

		appointment.Status = body.Status

		// If status is cancelled, set cancelled fields
		if body.Status == AppointmentStatusCancelled {
			appointment.CancelledAt = sql.NullTime{
				Time:  time.Now(),
				Valid: true,
			}
			appointment.CancelledByID = sql.NullString{
				String: user.ID,
				Valid:  true,
			}
		}

		if err := server.Db.Save(appointment).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]interface{}{"appointment": *appointment})
	}
}
