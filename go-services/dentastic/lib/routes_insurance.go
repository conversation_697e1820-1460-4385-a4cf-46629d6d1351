package lib

import (
	"encoding/json"
	"errors"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gorm.io/gorm"
)

const insuranceCompanyNotFoundError = "Insurance company not found"

type JsonDentalClinicInsuranceCompany struct {
	DentalClinicInsuranceCompany
	Clinic string `json:"omitempty"`
}

// GET: /insurance-companies
func HandleInsuranceCompaniesList(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		insuranceCompanies := []DentalClinicInsuranceCompany{}
		if err := server.Db.Where("clinic_id = ?", user.ClinicID).Find(&insuranceCompanies).Order("created_at DESC").Error; err != nil {
			panic(err)
		}
		jsonInsuranceCompanies := []JsonDentalClinicInsuranceCompany{}
		for _, insuranceCompany := range insuranceCompanies {
			jsonInsuranceCompanies = append(jsonInsuranceCompanies, JsonDentalClinicInsuranceCompany{
				DentalClinicInsuranceCompany: insuranceCompany,
				Clinic:                       user.Clinic.Name,
			})
		}
		return c.JSON(map[string]interface{}{"insuranceCompanies": jsonInsuranceCompanies})
	}
}

// POST: /insurance-companies
func HandleInsuranceCompanyCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name                      string  `json:"name"`
		DefaultPercentageCoverage float64 `json:"defaultPercentageCoverage"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		json.Unmarshal(c.Body(), &body)
		insuranceCompany := DentalClinicInsuranceCompany{
			Name:                      body.Name,
			DefaultPercentageCoverage: body.DefaultPercentageCoverage,
			ClinicID:                  user.ClinicID,
			Balance:                   0,
		}
		if err := server.Db.Create(&insuranceCompany).Error; err != nil {
			panic(err)
		}
		return c.Status(201).JSON(map[string]interface{}{"insuranceCompany": insuranceCompany})
	}
}

// PATCH: /insurance-companies/:id
func HandleInsuranceCompanyUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name                      string  `json:"name"`
		DefaultPercentageCoverage float64 `json:"defaultPercentageCoverage"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		json.Unmarshal(c.Body(), &body)
		companyId := c.Params("id")
		insuranceCompany := DentalClinicInsuranceCompany{
			Name:                      body.Name,
			DefaultPercentageCoverage: body.DefaultPercentageCoverage,
		}
		if err := server.Db.Model(&insuranceCompany).Where("id = ? AND clinic_id = ?", companyId, user.ClinicID).Updates(&insuranceCompany).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]interface{}{"message": insuranceCompanyNotFoundError})
			}
			panic(err)
		}
		return c.JSON(map[string]interface{}{"insuranceCompany": insuranceCompany})
	}
}

// DELETE: /insurance-companies/:id
func HandleInsuranceCompanyDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		companyId := c.Params("id")
		insuranceCompany := DentalClinicInsuranceCompany{}
		if err := server.Db.Where("id = ? AND clinic_id = ?", companyId, user.ClinicID).Delete(&insuranceCompany).Error; err != nil {
			panic(err)
		}
		return c.Status(204).JSON(map[string]interface{}{"insuranceCompany": insuranceCompany})
	}
}

// GET: /insurance-companies/:id/claims
func HandleInsuranceCompanyClaimsGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		companyId := c.Params("id")
		insuranceCompany := DentalClinicInsuranceCompany{}
		if err := server.Db.Where("id = ? AND clinic_id = ?", companyId, user.ClinicID).First(&insuranceCompany).Error; err != nil {
			panic(err)
		}
		claims := []DentalClinicInsuranceCompanyClaim{}
		if err := server.Db.Where("insurance_company_id = ?", insuranceCompany.ID).Joins("Patient").Find(&claims).Order("created_at DESC").Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"claims": claims})
	}
}

// POST: /insurance-companies/:id/claims
func HandleInsuranceCompanyClaimsCreate(server *config.Server) func(*fiber.Ctx) error {
	type claimItem struct {
		Treatment          string                                      `json:"treatment"`
		Price              float64                                     `json:"price"`
		Status             DentalClinicInsuranceCompanyClaimItemStatus `json:"status"`
		PercentageCoverage float64                                     `json:"percentageCoverage"`
	}
	type request struct {
		Items   []claimItem `json:"items"`
		VisitID string      `json:"visitId"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		companyId := c.Params("id")
		insuranceCompany := DentalClinicInsuranceCompany{}
		if err := server.Db.Where("id = ? AND clinic_id = ?", companyId, user.ClinicID).First(&insuranceCompany).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]interface{}{"message": insuranceCompanyNotFoundError})
			}
			panic(err)
		}
		body := new(request)
		json.Unmarshal(c.Body(), &body)
		visit := DentalClinicVisit{}
		if err := server.Db.Where("id = ? AND clinic_id = ?", body.VisitID, user.ClinicID).First(&visit).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]interface{}{"message": "Visit not found"})
			}
			panic(err)
		}
		claim := DentalClinicInsuranceCompanyClaim{
			ClinicID:           user.ClinicID,
			InsuranceCompanyID: insuranceCompany.ID,
			VisitID:            visit.ID,
			PatientID:          visit.PatientID,
		}
		claimItems := []DentalClinicInsuranceCompanyClaimItem{}
		err := server.Db.Transaction(func(tx *gorm.DB) error {
			if err := tx.Create(&claim).Error; err != nil {
				return err
			}
			for _, item := range body.Items {
				claimItem := DentalClinicInsuranceCompanyClaimItem{
					Treatment:          item.Treatment,
					Price:              item.Price,
					Status:             item.Status,
					PercentageCoverage: item.PercentageCoverage,
					ClaimID:            claim.ID,
				}
				claimItems = append(claimItems, claimItem)
			}
			if err := tx.Create(&claimItems).Error; err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			panic(err)
		}
		return c.Status(201).JSON(map[string]interface{}{"claim": claim, "items": claimItems})
	}
}

// GET: /insurance-companies/:id/claims/:claimId
func HandleInsuranceCompanyClaimsGetOne(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		companyId := c.Params("id")
		claimId := c.Params("claimId")
		insuranceCompany := DentalClinicInsuranceCompany{}
		if err := server.Db.Where("id = ? AND clinic_id = ?", companyId, user.ClinicID).First(&insuranceCompany).Error; err != nil {
			panic(err)
		}
		claim := DentalClinicInsuranceCompanyClaim{}
		if err := server.Db.Where("id = ? AND insurance_company_id = ?", claimId, insuranceCompany.ID).First(&claim).Error; err != nil {
			panic(err)
		}
		claimItems := []DentalClinicInsuranceCompanyClaimItem{}
		if err := server.Db.Where("claim_id = ?", claim.ID).Find(&claimItems).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"claim": claim, "items": claimItems})
	}
}

// PUT: /insurance-companies/:id/claims/:claimId
func HandleInsuranceCompanyClaimsUpdateOne(server *config.Server) func(*fiber.Ctx) error {
	type claimItem struct {
		Treatment          string                                      `json:"treatment"`
		Price              float64                                     `json:"price"`
		Status             DentalClinicInsuranceCompanyClaimItemStatus `json:"status"`
		PercentageCoverage float64                                     `json:"percentageCoverage"`
	}
	type request struct {
		Items []claimItem `json:"items"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		companyId := c.Params("id")
		claimId := c.Params("claimId")
		insuranceCompany := DentalClinicInsuranceCompany{}
		if err := server.Db.Where("id = ? AND clinic_id = ?", companyId, user.ClinicID).First(&insuranceCompany).Error; err != nil {
			panic(err)
		}
		claim := DentalClinicInsuranceCompanyClaim{}
		if err := server.Db.Where("id = ? AND insurance_company_id = ?", claimId, insuranceCompany.ID).First(&claim).Error; err != nil {
			panic(err)
		}
		body := new(request)
		json.Unmarshal(c.Body(), &body)
		claimItems := []DentalClinicInsuranceCompanyClaimItem{}
		err := server.Db.Transaction(func(tx *gorm.DB) error {
			for _, item := range body.Items {
				claimItem := DentalClinicInsuranceCompanyClaimItem{
					Treatment:          item.Treatment,
					Price:              item.Price,
					Status:             item.Status,
					PercentageCoverage: item.PercentageCoverage,
					ClaimID:            claim.ID,
				}
				claimItems = append(claimItems, claimItem)
			}
			if err := tx.Delete(&DentalClinicInsuranceCompanyClaimItem{}, "claim_id = ?", claim.ID).Error; err != nil {
				return err
			}
			if err := tx.Create(&claimItems).Error; err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			panic(err)
		}
		return c.Status(200).JSON(map[string]interface{}{"claim": claim, "items": claimItems})
	}
}

// DELETE: /insurance-companies/:id/claims/:claimId
func HandleInsuranceCompanyClaimsDeleteOne(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		companyId := c.Params("id")
		claimId := c.Params("claimId")
		insuranceCompany := DentalClinicInsuranceCompany{}
		if err := server.Db.Where("id = ? AND clinic_id = ?", companyId, user.ClinicID).First(&insuranceCompany).Error; err != nil {
			panic(err)
		}
		claim := DentalClinicInsuranceCompanyClaim{}
		if err := server.Db.Where("id = ? AND insurance_company_id = ?", claimId, insuranceCompany.ID).First(&claim).Error; err != nil {
			panic(err)
		}
		claimItems := []DentalClinicInsuranceCompanyClaimItem{}
		if err := server.Db.Where("claim_id = ?", claim.ID).Find(&claimItems).Error; err != nil {
			panic(err)
		}
		err := server.Db.Transaction(func(tx *gorm.DB) error {
			if err := tx.Delete(&claimItems).Error; err != nil {
				return err
			}
			if err := tx.Delete(&claim).Error; err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			panic(err)
		}
		return c.Status(200).JSON(map[string]interface{}{"message": "Claim deleted"})
	}
}
