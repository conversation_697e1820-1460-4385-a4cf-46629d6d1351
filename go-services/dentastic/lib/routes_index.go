package lib

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"regexp"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/kataras/jwt"
	"github.com/stripe/stripe-go/v74"
	"github.com/stripe/stripe-go/v74/customer"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/core/responses"
	mailer "gitlab.com/payrows/messaging/email"
	"gitlab.com/payrows/messaging/sms"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// POST: /login
func HandleLogin(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		ClinicName string `json:"clinicName"`
		Username   string `json:"username"`
		Password   string `json:"password"`
	}
	return func(c *fiber.Ctx) error {
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		clinicName := strings.TrimSpace(body.ClinicName)
		username := strings.TrimSpace(body.Username)
		if len(clinicName) == 0 {
			return responses.Send400(c, "Clinic name missing")
		}
		clinic := new(DentalClinic)
		if err = server.Db.Where("name ILIKE ?", clinicName).Take(clinic).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Clinic not found"})
			}
			panic(err)
		}
		user := new(DentalClinicUser)
		if err = server.Db.Where("clinic_id = ? AND username ILIKE ?", clinic.ID, username).Take(user).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return responses.Send401(c)
			}
			panic(err)
		}
		if string(user.Role) == string(DentalClinicRoleExternal) {
			log.Println("User is external")
			return c.Status(401).JSON(map[string]string{"error": "Cannot log in as an external user"})
		}
		err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(body.Password))
		if err != nil {
			return responses.Send401(c)
		}
		// expiry, err := time.Parse(time.RFC3339, clinic.SubscriptionEndDate)
		// if err != nil {
		// 	panic(err)
		// }
		// tokenExpiry := expiry
		// if expiry.Before(time.Now()) {
		// 	tokenExpiry = time.Now().Add(24 * time.Hour)
		// }
		tokenExpiry := time.Now().Add(7 * 24 * time.Hour)
		log.Println(clinic.SubscriptionRenewedAt)
		subscriptionRenewedAt, err := time.Parse(time.RFC3339, clinic.SubscriptionRenewedAt)
		if err != nil {
			log.Println("failed to get datetime from date string in subscription renewed at. Giving customer free access:", err)
			subscriptionRenewedAt = time.Now()
		}
		token, err := jwt.Sign(server.JWTAlgorithm, server.JWTSecret, map[string]interface{}{"merchant": clinic.MerchantID, "id": user.ID, "clinicId": clinic.ID, "type": models.MerchantTypeDentalClinic, "role": string(user.Role) /* "subscriptionEndDate": expiry.Unix(), */, "subscriptionRenewedAt": subscriptionRenewedAt.Unix(), "exp": tokenExpiry.Unix()})
		if err != nil {
			panic(err)
		}
		return c.JSON(map[string]string{"token": string(token)})
	}
}

// POST: /register
// POST: /clinics
func HandleMerchantCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		DisplayName string `json:"displayName"`
		Username    string `json:"username"`
		Name        string `json:"name"`
		Email       string `json:"email"`
		Password    string `json:"password"`
		PhoneNumber string `json:"phoneNumber"`
		SystemName  string `json:"systemName"`
		// Type        string `json:"type"`
		// SubscriptionPlan string `json:"subscriptionPlan"`
	}
	return func(c *fiber.Ctx) error {
		user := new(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		merchant := new(models.Merchant)
		clinic := new(DentalClinic)
		plan := new(DentalClinicSubscriptionPlan)
		plan.ID = models.MakeID()
		plan.Name = body.SystemName
		plan.Active = true
		plan.Price = 0
		plan.CanAddUsers = true
		plan.CanSendSMSs = true
		plan.Currency = "USD"
		plan.PricePerVisit = 0.99
		plan.PricePerSMS = 0.19
		/* if body.SubscriptionPlan != "" {
			if err = server.Db.Where("id = ?", body.SubscriptionPlan).Take(plan).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return responses.Send400(c, "Invalid subscription plan")
				}
				panic(err)
			}
		} */
		if len(body.DisplayName) == 0 {
			return responses.Send400(c, "Missing clinic display name")
		}
		if len(body.Name) == 0 {
			body.Name = body.Username
			return responses.Send400(c, "Missing user display nam")
		}
		if !ValidateUsername(body.Username) {
			return responses.Send400(c, "Invalid username")
		}
		if len(body.Email) == 0 {
			return responses.Send400(c, "Missing email")
		}
		if len(body.Password) < 8 {
			return responses.Send400(c, "Password too short")
		}
		if err = server.Db.Where("email = ? OR phone_number = ?", body.Email, body.PhoneNumber).Take(merchant).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				panic(err)
			}
		}
		if len(merchant.ID) > 0 {
			return c.Status(409).JSON(map[string]interface{}{"error": "User with same email or phone number already exists"})
		}
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(body.Password), 10)
		if err != nil {
			panic(err)
		}
		body.SystemName = strings.TrimSpace(body.SystemName)
		if len(body.SystemName) == 0 {
			return responses.Send400(c, "Missing system/clinic name")
		}
		/* uniqueName := strings.ReplaceAll(strings.ToLower(body.DisplayName), " ", "")
		suffix, err := gonanoid.New(5)
		if err != nil {
			panic(err)
		}
		uniqueName = uniqueName + "-" + suffix */
		conflictSystemNameMerchant := new(models.Merchant)
		if err = server.Db.Where("system_name = ?", body.SystemName).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			panic(err)
		}
		if len(conflictSystemNameMerchant.ID) > 0 {
			return responses.Send409(c, "System name already taken")
		}
		stripeCustomer, err := customer.New(&stripe.CustomerParams{
			Name:  stripe.String(body.Username),
			Email: stripe.String(body.Email),
			Params: stripe.Params{
				Metadata: map[string]string{
					"merchant_id": merchant.ID,
				},
			},
		})
		if err != nil {
			panic(err)
		}
		merchant.DisplayName = strings.TrimSpace(body.DisplayName)
		// merchant.SystemName = uniqueName /* strings.TrimSpace(strings.ToLower(body.SystemName)) */
		merchant.SystemName = body.SystemName
		merchant.Email = strings.TrimSpace(body.Email)
		merchant.PhoneNumber = strings.TrimSpace(body.PhoneNumber)
		merchant.Type = models.MerchantTypeDentalClinic
		merchant.StripeCustomerId = sql.NullString{Valid: true, String: stripeCustomer.ID}
		merchant.Password = string(hashedPassword)
		clinic.DisplayName = body.DisplayName
		// clinic.Name = uniqueName
		clinic.Name = body.SystemName
		// if plan != nil {
		clinic.SubscriptionPlanID = sql.NullString{
			String: plan.ID,
			Valid:  true,
		}
		// }
		now := time.Now()
		// expiry := time.Date(now.Year()+1, now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
		expiry := time.Now().AddDate(0, 1, 0) // add 1 month instead of 1 year
		tokenExpiry := time.Now().Add(30 * 24 * time.Hour)
		clinic.SubscriptionEndDate = FormatDateToString(expiry)
		clinic.SubscriptionRenewedAt = FormatDateToString(now)
		// TODO: setup currency instead of default EGP
		username := body.Username
		if len(username) == 0 {
			username = "master"
		}
		user.Username = username
		user.Name = body.Name
		user.Role = DentalClinicRoleMaster
		user.Password = string(hashedPassword)
		user.IsDentist = true
		var token []byte
		err = server.Db.Transaction(func(tx *gorm.DB) error {
			if err = tx.Create(plan).Error; err != nil {
				return err
			}
			if err = tx.Create(merchant).Error; err != nil {
				return err
			}
			clinic.MerchantID = merchant.ID
			if err = tx.Create(clinic).Error; err != nil {
				return err
			}
			user.ClinicID = clinic.ID
			if err = tx.Create(user).Error; err != nil {
				return err
			}
			token, err = jwt.Sign(server.JWTAlgorithm, server.JWTSecret, map[string]interface{}{"merchant": merchant.ID, "id": user.ID, "clinicId": clinic.ID, "type": models.MerchantTypeDentalClinic, "subscriptionRenewedAt": now.Unix() /* "subscriptionEndDate": expiry.Unix(), */, "exp": tokenExpiry.Unix()})
			// For verbosity
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			_, err := customer.Del(stripeCustomer.ID, nil)
			if err != nil {
				log.Println("Failed to delete stripe customer: ", err.Error())
			}
			panic(err)
		}
		err = c.Status(201).JSON(map[string]string{"token": string(token)})
		if err != nil {
			panic(err)
		}
		// NOTE: expected sendgrid API key in environment variables
		err = mailer.SendSendGridEmail(&mailer.SendGridEmailRequest{
			EmailRequest: mailer.EmailRequest{
				From:    "<EMAIL>",
				Subject: "New Sign Up",
				Body:    "A new user just signed up. Details:\nClinic Name: " + merchant.DisplayName + "\nDentist Name: " + body.Name,
			},
			To: []string{"<EMAIL>", "<EMAIL>"},
		})
		if err != nil {
			panic(err)
		}
		return nil
	}
}

// PATCH: /clinics
func HandleMerchantUpdate(server *config.Server) func(*fiber.Ctx) error {
	type patientNotificationConfigRequest struct {
		Language        *DentalClinicSupportedNotificationLanguage `json:"language"`
		DaysPrior       *int                                       `json:"daysPrior"`
		IncludeLocation *bool                                      `json:"includeLocation"`
	}
	type request struct {
		DisplayName               string                            `json:"displayName"`
		PatientNotificationConfig *patientNotificationConfigRequest `json:"patientNotificationConfig"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		merchant := new(models.Merchant)
		if err = server.Db.Where("id = ?", user.Clinic.MerchantID).Take(merchant).Error; err != nil {
			panic(err)
		}
		clinic := new(DentalClinic)
		if err = server.Db.Where("id = ?", user.ClinicID).Take(clinic).Error; err != nil {
			panic(err)
		}
		if len(body.DisplayName) > 0 {
			merchant.DisplayName = body.DisplayName
			clinic.DisplayName = body.DisplayName
		}
		clinicPatientNotificationConfig := new(DentalClinicPatientNotificationConfig)
		if body.PatientNotificationConfig != nil {
			if err = server.Db.Where("clinic_id = ?", clinic.ID).Take(clinicPatientNotificationConfig).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					clinicPatientNotificationConfig.ClinicID = clinic.ID
					clinicPatientNotificationConfig.Language = EnglishDentalClinicSupportedNotificationLanguage
					clinicPatientNotificationConfig.DaysPrior = 1
					clinicPatientNotificationConfig.IncludeLocation = false
				} else {
					panic(err)
				}
			}
			if body.PatientNotificationConfig.Language != nil {
				clinicPatientNotificationConfig.Language = *body.PatientNotificationConfig.Language
			}
			if body.PatientNotificationConfig.DaysPrior != nil {
				clinicPatientNotificationConfig.DaysPrior = *body.PatientNotificationConfig.DaysPrior
			}
			if body.PatientNotificationConfig.IncludeLocation != nil {
				clinicPatientNotificationConfig.IncludeLocation = *body.PatientNotificationConfig.IncludeLocation
			}
		}
		err = server.Db.Transaction(func(tx *gorm.DB) error {
			if err = tx.Save(merchant).Error; err != nil {
				return err
			}
			if err = tx.Save(clinic).Error; err != nil {
				return err
			}
			if clinicPatientNotificationConfig.ID != "" {
				if err = tx.Save(clinicPatientNotificationConfig).Error; err != nil {
					return err
				}
			}
			return nil
		})
		if err != nil {
			panic(err)
		}
		return c.JSON(merchant)
	}
}

// POST: /clinics/payment-methods
func HandleMerchantPaymentMethodCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		ID       string `json:"id"`
		Type     string `json:"type"`
		LiveMode bool   `json:"livemode"`
		// Object   string `json:"object"`
	}
	return func(c *fiber.Ctx) error {
		// merchant := c.UserContext().Value(auth.AuthenticatedUserTypeMerchant).(models.Merchant)
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		if !body.LiveMode {
			log.Println("Payment method created in test mode")
		}
		paymentMethod := &models.MerchantPaymentMethod{
			MerchantRef: models.MerchantRef{
				// MerchantID: merchant.ID,
				MerchantID: user.Clinic.MerchantID,
			},
			Provider:    "stripe",
			ProviderRef: body.ID,
			Type:        body.Type,
		}
		if err = server.Db.Create(paymentMethod).Error; err != nil {
			panic(err)
		}
		return c.Status(201).JSON(paymentMethod)
	}
}
func HandleClinicSync(server *config.Server) func(*fiber.Ctx) error {
	type offlineSpecialityTemplate struct {
		JsonSpecialityTemplate
		ID        interface{} `json:"id"`
		Offline   bool        `json:"offline"`
		DeletedAt *string     `json:"deletedAt"`
	}
	type offlineProcedureTemplate struct {
		JsonProcedureTemplate
		ID           interface{} `json:"id"`
		Offline      bool        `json:"offline"`
		DeletedAt    *string     `json:"deletedAt"`
		SpecialityID interface{} `json:"specialityId"`
	}
	type offlinePatient struct {
		JsonPatient
		ID        interface{} `json:"id"`
		Offline   bool        `json:"offline"`
		DeletedAt *string     `json:"deletedAt"`
	}
	type offlineAppointment struct {
		JsonAppointment
		ID        interface{} `json:"id"`
		Offline   bool        `json:"offline"`
		DeletedAt *string     `json:"deletedAt"`
		PatientID interface{} `json:"patientId"`
	}
	type offlineVisit struct {
		JsonVisit
		ID        interface{} `json:"id"`
		Offline   bool        `json:"offline"`
		DeletedAt *string     `json:"deletedAt"`
		PatientID interface{} `json:"patientId"`
	}
	type offlineProcedure struct {
		JsonProcedure
		ID                  interface{} `json:"id"`
		Offline             bool        `json:"offline"`
		DeletedAt           *string     `json:"deletedAt"`
		VisitID             interface{} `json:"visitId"`
		ProcedureTemplateID interface{} `json:"procedureTemplateId"`
	}
	type offlinePatientPayment struct {
		ID        interface{} `json:"id"`
		Offline   bool        `json:"offline"`
		DeletedAt *string     `json:"deletedAt"`
		VisitID   interface{} `json:"visitId"`
		PatientID interface{} `json:"patientId"`
	}
	type offlineExpense struct {
		JsonExpense
		ID        interface{} `json:"id"`
		Offline   bool        `json:"offline"`
		DeletedAt *string     `json:"deletedAt"`
	}
	type offlineInventoryItem struct {
		JsonItem
		ID        interface{} `json:"id"`
		Offline   bool        `json:"offline"`
		DeletedAt *string     `json:"deletedAt"`
	}
	type request struct {
		Specialities       []offlineSpecialityTemplate `json:"specialities"`
		ProcedureTemplates []offlineProcedureTemplate  `json:"procedureTemplates"`
		Patients           []offlinePatient            `json:"patients"`
		Appointments       []offlineAppointment        `json:"appointments"`
		Visits             []offlineVisit              `json:"visits"`
		Procedures         []offlineProcedure          `json:"procedures"`
		PatientPayments    []offlinePatientPayment     `json:"patientPayments"`
		Expenses           []offlineExpense            `json:"expenses"`
		InventoryItems     []offlineInventoryItem      `json:"inventoryItems"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		var specialitiesCreated []DentalClinicSpecialityTemplate
		specialitiesUpdated := map[string]interface{}{}
		var specialitiesDeleted []string
		for _, v := range body.Specialities {
			if v.Offline {
				// Create
				specialitiesCreated = append(specialitiesCreated, v.DentalClinicSpecialityTemplate)
			} else {
				instance := new(DentalClinicSpecialityTemplate)
				if err := server.Db.Where("id = ? AND clinic_id = ?", v.ID.(string), user.ClinicID).Take(instance).Error; err != nil {
					if errors.Is(err, gorm.ErrRecordNotFound) {
						return c.Status(404).JSON(map[string]string{"error": "Speciality not found"})
					}
					panic(err)
				}
				if v.DeletedAt == nil {
					// Update
					// TODO: check if another update happened after the update provided offline
					instance.Speciality = v.JsonSpecialityTemplate.Speciality
					specialitiesUpdated[instance.ID] = instance
				} else {
					// Delete
					specialitiesDeleted = append(specialitiesDeleted, v.ID.(string))
				}
			}
		}
		if err := server.Db.Transaction(func(tx *gorm.DB) error {
			if err := tx.Model(DentalClinicSpecialityTemplate{}).Create(specialitiesCreated).Error; err != nil {
				return err
			}
			if err := tx.Delete(&DentalClinicSpecialityTemplate{}, specialitiesDeleted).Error; err != nil {
				return err
			}
			return nil
		}); err != nil {
			panic(err)
		}
		return c.JSON(body)
	}
}
func ValidateUsername(username string) bool {
	match, err := regexp.MatchString(`^[a-zA-Z0-9_\-\.]{2,}$`, username)
	if err != nil {
		panic(err)
	}
	return match
}

func GenerateOTP(server *config.Server, clinicID, userID string) (string, error) {
	otp := fmt.Sprintf("%06d", rand.Intn(1000000))
	otpRecord := DentalClinicUserOTP{
		ClinicID:  clinicID,
		UserID:    userID,
		OTP:       otp,
		ExpiresAt: time.Now().Add(15 * time.Minute),
		Used:      false,
	}
	if err := server.Db.Create(&otpRecord).Error; err != nil {
		return "", err
	}
	return otp, nil
}

// POST: /forgot-password
func HandleForgotPassword(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		ClinicName string `json:"clinicName"`
		Username   string `json:"username"`
	}

	return func(c *fiber.Ctx) error {
		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			panic(err)
		}

		clinicName := strings.TrimSpace(body.ClinicName)
		username := strings.TrimSpace(body.Username)

		if len(clinicName) == 0 {
			return responses.Send400(c, "Clinic name missing")
		}
		if len(username) == 0 {
			return responses.Send400(c, "Username missing")
		}

		clinic := new(DentalClinic)
		if err := server.Db.Where("name ILIKE ?", clinicName).Take(clinic).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Clinic not found"})
			}
			panic(err)
		}

		user := new(DentalClinicUser)
		if err := server.Db.Where("clinic_id = ? AND username ILIKE ?", clinic.ID, username).Take(user).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]interface{}{"error": "User not found"})
			}
			panic(err)
		}

		merchant := new(models.Merchant)
		if string(user.Role) == string(DentalClinicRoleMaster) || user.PhoneNumber == "" {
			if err := server.Db.Where("id = ?", clinic.MerchantID).Take(merchant).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					return responses.Send500(c)
				}
				panic(err)
			}
		}

		if string(user.Role) == string(DentalClinicRoleMaster) {
			// Email-based OTP for master users
			otp, err := GenerateOTP(server, clinic.ID, user.ID)
			if err != nil {
				panic(err)
			}

			err = mailer.SendSendGridEmail(&mailer.SendGridEmailRequest{
				EmailRequest: mailer.EmailRequest{
					From:    "<EMAIL>",
					Subject: "Password Reset OTP",
					Body:    fmt.Sprintf("Your password reset OTP is: %s. Valid for 15 minutes.", otp),
				},
				To: []string{merchant.Email},
			})
			if err != nil {
				log.Println("Failed to send OTP email:", err)
				return responses.Send500(c)
			}

			return c.JSON(map[string]string{
				"message": "OTP sent to your registered email",
				"code":    "otp_sent_email",
			})
		} else if user.PhoneNumber != "" {
			// Phone-based OTP
			otp, err := GenerateOTP(server, clinic.ID, user.ID)
			if err != nil {
				panic(err)
			}

			err = sms.SendSMS(&sms.SMSRequest{
				SenderID:  "NAAB",
				Body:      fmt.Sprintf("Your password reset OTP is: %s. Valid for 15 minutes.", otp),
				Recipient: user.PhoneNumber,
			})
			if err != nil {
				log.Println("Failed to send OTP SMS:", err)
				return responses.Send500(c)
			}

			return c.JSON(map[string]string{
				"message": "OTP sent to your registered phone number",
				"code":    "otp_sent_sms",
			})
		} else {
			err := mailer.SendSendGridEmail(&mailer.SendGridEmailRequest{
				EmailRequest: mailer.EmailRequest{
					From:    "<EMAIL>",
					Subject: "Password Reset Request",
					Body:    fmt.Sprintf("User '%s' from clinic '%s' has requested a password reset. Please assist them in resetting their password.", user.Name, clinic.DisplayName),
				},
				To: []string{merchant.Email},
			})
			if err != nil {
				log.Println("Failed to send email to masters:", err)
				return responses.Send500(c)
			}

			return c.JSON(map[string]string{
				"message": "Request sent to clinic administrators",
				"code":    "request_sent",
			})
		}
	}
}

// POST: /verify-otp
func HandleVerifyOTP(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		ClinicName string `json:"clinicName"`
		Username   string `json:"username"`
		OTP        string `json:"otp"`
	}
	return func(c *fiber.Ctx) error {
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		clinicName := strings.TrimSpace(body.ClinicName)
		username := strings.TrimSpace(body.Username)
		otp := strings.TrimSpace(body.OTP)

		if len(clinicName) == 0 {
			return responses.Send400(c, "Clinic name missing")
		}
		if len(username) == 0 {
			return responses.Send400(c, "Username missing")
		}
		if len(otp) == 0 {
			return responses.Send400(c, "OTP missing")
		}

		clinic := new(DentalClinic)
		if err = server.Db.Where("name ILIKE ?", clinicName).Take(clinic).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]string{"error": "Clinic not found"})
			}
			panic(err)
		}

		user := new(DentalClinicUser)
		if err = server.Db.Where("clinic_id = ? AND username ILIKE ?", clinic.ID, username).Take(user).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]interface{}{"error": "User not found"})
			}
			panic(err)
		}

		otpRecord := new(DentalClinicUserOTP)
		if err = server.Db.Where("clinic_id = ? AND user_id = ? AND otp = ? AND used = ? AND expires_at > ?",
			clinic.ID, user.ID, otp, false, time.Now()).
			Order("created_at DESC").
			Take(otpRecord).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return responses.Send400(c, "Invalid or expired OTP")
			}
			panic(err)
		}

		tokenExpiry := time.Now().Add(5 * time.Minute)
		token, err := jwt.Sign(server.JWTAlgorithm, server.JWTSecret, map[string]interface{}{
			"clinicId": clinic.ID,
			"userId":   user.ID,
			"purpose":  "password-reset",
			"exp":      tokenExpiry.Unix(),
		})
		if err != nil {
			panic(err)
		}

		otpRecord.Used = true
		if err = server.Db.Save(otpRecord).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]string{"token": string(token)})
	}
}

// POST: /reset-password
func HandleResetPassword(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Token       string `json:"token"`
		NewPassword string `json:"newPassword"`
	}
	return func(c *fiber.Ctx) error {
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		token := strings.TrimSpace(body.Token)
		newPassword := strings.TrimSpace(body.NewPassword)

		if len(token) == 0 {
			return responses.Send400(c, "Token missing")
		}
		if len(newPassword) < 8 {
			return responses.Send400(c, "Password too short (minimum 8 characters)")
		}

		verifiedToken, err := jwt.Verify(server.JWTAlgorithm, server.JWTSecret, []byte(token))
		if err != nil {
			return responses.Send401(c)
		}

		var claims map[string]interface{}
		if err = verifiedToken.Claims(&claims); err != nil {
			panic(err)
		}

		purpose, ok := claims["purpose"].(string)
		if !ok || purpose != "password-reset" {
			return responses.Send401(c)
		}

		clinicID, ok := claims["clinicId"].(string)
		if !ok {
			return responses.Send401(c)
		}

		userID, ok := claims["userId"].(string)
		if !ok {
			return responses.Send401(c)
		}

		user := new(DentalClinicUser)
		if err = server.Db.Where("id = ? AND clinic_id = ?", userID, clinicID).Take(user).Error; err != nil {
			if errors.Is(gorm.ErrRecordNotFound, err) {
				return c.Status(404).JSON(map[string]interface{}{"error": "User not found"})
			}
			panic(err)
		}

		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), 10)
		if err != nil {
			panic(err)
		}

		user.Password = string(hashedPassword)
		if err = server.Db.Save(user).Error; err != nil {
			panic(err)
		}

		return c.JSON(map[string]string{"message": "Password updated successfully"})
	}
}
