package lib

import (
	"os"
	"strings"

	"gorm.io/gorm"
)

// DatabaseCompatibility provides database-specific query compatibility
type DatabaseCompatibility struct {
	isSQLite bool
}

// NewDatabaseCompatibility creates a new database compatibility helper
func NewDatabaseCompatibility() *DatabaseCompatibility {
	dbURL := os.Getenv("DB_URL")
	isSQLite := strings.HasPrefix(dbURL, "sqlite://")
	
	return &DatabaseCompatibility{
		isSQLite: isSQLite,
	}
}

// ILike performs a case-insensitive LIKE query compatible with both PostgreSQL and SQLite
func (dc *DatabaseCompatibility) ILike(db *gorm.DB, column, value string) *gorm.DB {
	if dc.isSQLite {
		// SQLite: Use LIKE with COLLATE NOCASE for case-insensitive search
		return db.Where(column+" LIKE ? COLLATE NOCASE", value)
	} else {
		// PostgreSQL: Use ILIKE
		return db.Where(column+" ILIKE ?", value)
	}
}

// ILikeCondition returns the appropriate condition string for case-insensitive LIKE
func (dc *DatabaseCompatibility) ILikeCondition(column string) string {
	if dc.isSQLite {
		return column + " LIKE ? COLLATE NOCASE"
	} else {
		return column + " ILIKE ?"
	}
}

// Global instance for easy access
var DBCompat = NewDatabaseCompatibility()

// Helper functions for common patterns

// FindClinicByName finds a clinic by name using case-insensitive search
func FindClinicByName(db *gorm.DB, name string) (*DentalClinic, error) {
	clinic := new(DentalClinic)
	err := DBCompat.ILike(db, "name", name).Take(clinic).Error
	return clinic, err
}

// FindUserByUsernameAndClinic finds a user by username and clinic ID using case-insensitive search
func FindUserByUsernameAndClinic(db *gorm.DB, clinicID, username string) (*DentalClinicUser, error) {
	user := new(DentalClinicUser)
	
	if DBCompat.isSQLite {
		err := db.Where("clinic_id = ? AND username LIKE ? COLLATE NOCASE", clinicID, username).Take(user).Error
		return user, err
	} else {
		err := db.Where("clinic_id = ? AND username ILIKE ?", clinicID, username).Take(user).Error
		return user, err
	}
}

// FindUserByUsernameAndClinicName finds a user by username and clinic name using case-insensitive search
func FindUserByUsernameAndClinicName(db *gorm.DB, clinicName, username string) (*DentalClinicUser, *DentalClinic, error) {
	clinic, err := FindClinicByName(db, clinicName)
	if err != nil {
		return nil, nil, err
	}
	
	user, err := FindUserByUsernameAndClinic(db, clinic.ID, username)
	if err != nil {
		return nil, clinic, err
	}
	
	return user, clinic, nil
}
