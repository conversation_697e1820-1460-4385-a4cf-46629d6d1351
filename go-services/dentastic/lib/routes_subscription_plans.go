package lib

import (
	"encoding/json"
	"errors"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type JsonSubscriptionPlan struct {
	DentalClinicSubscriptionPlan
	CreatedBy   string `json:"createdBy,omitempty"`
	UpdatededBy string `json:"updatedBy,omitempty"`
}

// GET: /subscription-plans
func HandleSubscriptionPlansGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		var plans []DentalClinicSubscriptionPlan
		if err := server.Db.Find(&plans).Error; err != nil {
			panic(err)
		}

		jsonPlans := make([]JsonSubscriptionPlan, len(plans))
		for index, v := range plans {
			jsonPlans[index] = JsonSubscriptionPlan{DentalClinicSubscriptionPlan: v}
		}

		return c.J<PERSON>N(map[string]interface{}{"subscriptionPlans": jsonPlans})
	}
}

// POST: /subscription-plans
func HandleSubscriptionPlanCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name          string  `json:"name"`
		Price         float64 `json:"price"`
		Currency      string  `json:"currency"`
		PricePerVisit float64 `json:"pricePerVisit"`
		PricePerSMS   float64 `json:"pricePerSMS"`
		CanAddUsers   *bool   `json:"canAddUsers,omitempty"`
		CanSendSMSs   bool    `json:"canSendSMSs"`
	}
	return func(c *fiber.Ctx) error {
		admin := c.UserContext().Value("admin").(models.Admin)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		plan := new(DentalClinicSubscriptionPlan)
		plan.Name = body.Name
		plan.Price = body.Price
		plan.Currency = body.Currency
		plan.PricePerVisit = body.PricePerVisit
		plan.PricePerSMS = body.PricePerSMS
		plan.Active = true
		plan.CreatedByID = admin.ID
		if body.CanAddUsers != nil {
			plan.CanAddUsers = *body.CanAddUsers
		}
		plan.CanSendSMSs = body.CanSendSMSs

		if err = server.Db.Create(plan).Error; err != nil {
			panic(err)
		}
		return c.Status(201).JSON(map[string]interface{}{"subscriptionPlan": *plan})
	}
}

// PATCH: /subscription-plans/:pid
func HandleSubscriptionPlanUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name          string  `json:"name"`
		Price         float64 `json:"price"`
		Currency      string  `json:"currency"`
		PricePerVisit float64 `json:"pricePerVisit"`
		PricePerSMS   float64 `json:"pricePerSMS"`
		Active        *bool   `json:"active,omitempty"`
		CanAddUsers   *bool   `json:"canAddUsers,omitempty"`
		CanSendSMSs   *bool   `json:"canSendSMSs,omitempty"`
	}
	return func(c *fiber.Ctx) error {
		pid := c.Params("pid")
		admin := c.UserContext().Value("admin").(models.Admin)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		plan := new(DentalClinicSubscriptionPlan)
		if err = server.Db.Where("id = ?", pid).Take(plan).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Subscription plan not found"})
			}
			panic(err)
		}

		updateObj := map[string]interface{}{}

		if len(body.Name) != 0 {
			updateObj["name"] = body.Name
		}
		if body.Price >= 0 {
			updateObj["price"] = body.Price
		}
		if len(body.Currency) > 0 {
			updateObj["currency"] = body.Currency
		}
		if body.PricePerVisit >= 0 {
			updateObj["price_per_visit"] = body.PricePerVisit
		}
		if body.PricePerSMS >= 0 {
			updateObj["price_per_sms"] = body.PricePerSMS
		}
		if body.Active != nil {
			updateObj["active"] = *body.Active
		}
		if body.CanAddUsers != nil {
			updateObj["can_add_users"] = *body.CanAddUsers
		}
		if body.CanSendSMSs != nil {
			updateObj["can_send_SMSs"] = *body.CanSendSMSs
		}
		if len(updateObj) == 0 {
			return responses.Send400(c, "Nothing to update")
		}

		updateObj["updated_by_id"] = admin.ID

		if err = server.Db.Model(plan).Clauses(clause.Returning{}).UpdateColumns(updateObj).Error; err != nil {
			panic(err)
		}

		return c.Status(200).JSON(map[string]interface{}{"subscriptionPlan": *plan})
	}
}
