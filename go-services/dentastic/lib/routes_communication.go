package lib

import (
	"database/sql"
	"encoding/json"
	"errors"
	"log"
	"strconv"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gorm.io/gorm"

	// "gitlab.com/payrows/core/messaging"
	messaging "gitlab.com/payrows/messaging/sms"
)

func HandleCommunicationSendSMS(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Recipients     []string `json:"recipients"`
		PatientGroupId *string  `json:"patientGroupId"`
		All            bool     `json:"all"`
		Message        string   `json:"message"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		if err := json.Unmarshal(c.Body(), body); err != nil {
			return c.Status(400).JSON(map[string]interface{}{"error": "Invalid request body"})
		}

		// Check SMS quota
		var smsQuota DentalClinicSMSQuota
		if err := server.Db.Where("clinic_id = ?", user.ClinicID).First(&smsQuota).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(400).JSON(map[string]interface{}{"error": "No SMS quota found for clinic"})
			}
			panic(err)
		}

		// Check if quota is expired
		if time.Now().After(smsQuota.PackageExpiryDate) {
			return c.Status(400).JSON(map[string]interface{}{"error": "SMS quota package has expired"})
		}

		// Count total recipients
		var totalRecipients int64
		if body.All {
			if err := server.Db.Model(&DentalClinicPatient{}).Where("clinic_id = ? AND phone_number IS NOT NULL", user.ClinicID).Count(&totalRecipients).Error; err != nil {
				panic(err)
			}
		} else if body.PatientGroupId != nil {
			var groupMembers []DentalClinicPatientGroupMember
			if err := server.Db.Where("clinic_id = ? AND id = ?", user.ClinicID, body.PatientGroupId).Find(&groupMembers).Error; err != nil {
				panic(err)
			}
			memberIds := make([]string, len(groupMembers))
			for _, member := range groupMembers {
				memberIds = append(memberIds, member.ID)
			}
			if err := server.Db.Model(&DentalClinicPatient{}).Where("clinic_id = ? AND id IN ? AND phone_number IS NOT NULL", user.ClinicID, memberIds).Count(&totalRecipients).Error; err != nil {
				panic(err)
			}
		} else {
			totalRecipients = int64(len(body.Recipients))
		}

		// Check if we have enough quota
		if totalRecipients > int64(smsQuota.RemainingLocalSMS) {
			return c.Status(400).JSON(map[string]interface{}{"error": "Insufficient SMS quota"})
		}

		wg := sync.WaitGroup{}
		counter := 0
		var patients []DentalClinicPatient
		if body.All {
			if err := server.Db.Select("id", "phone_number").Where("clinic_id = ? AND phone_number IS NOT NULL", user.ClinicID).Find(&patients).Error; err != nil {
				panic(err)
			}
		} else {
			if body.PatientGroupId != nil {
				var patientGroup DentalClinicPatientGroup
				if err := server.Db.Where("clinic_id = ? AND id = ?", user.ClinicID, body.PatientGroupId).First(&patientGroup).Error; err != nil {
					if !errors.Is(err, gorm.ErrRecordNotFound) {
						panic(err)
					} else {
						return c.Status(404).JSON(map[string]string{"error": "Patient group not found"})
					}
				}
				var groupMembers []DentalClinicPatientGroupMember
				if err := server.Db.Where("clinic_id = ? AND id = ?", user.ClinicID, body.PatientGroupId).Find(&groupMembers).Error; err != nil {
					panic(err)
				}
				memberIds := make([]string, len(groupMembers))
				for _, member := range groupMembers {
					memberIds = append(memberIds, member.ID)
				}
				if err := server.Db.Select("id", "phone_number").Where("clinic_id = ? AND id IN ? AND phone_number IS NOT NULL", user.ClinicID, memberIds).Find(&patients).Error; err != nil {
					panic(err)
				}
			} else {
				if err := server.Db.Select("id", "phone_number").Where("clinic_id = ? AND id IN ? AND phone_number IS NOT NULL", user.ClinicID, body.Recipients).Find(&patients).Error; err != nil {
					panic(err)
				}
			}
		}

		// Create a mutex for updating the quota
		var quotaMutex sync.Mutex

		for _, v := range patients {
			if v.PhoneNumber != "" {
				wg.Add(1)
				go func(v DentalClinicPatient) {
					err := messaging.SendSMS(&messaging.SMSRequest{
						Recipient: v.PhoneNumber,
						Body:      body.Message,
					})
					if err != nil {
						log.Println(err)
					} else {
						comm := &DentalClinicCommunication{
							ClinicID:       user.ClinicID,
							Body:           body.Message,
							Recipient:      v.PhoneNumber,
							Method:         "SMS",
							Price:          0.30,
							Currency:       "EGP",
							TargetingGroup: "All",
						}
						if err := server.Db.Create(comm).Error; err != nil {
							log.Println(err)
						}
						// Update quota atomically
						quotaMutex.Lock()
						smsQuota.RemainingLocalSMS--
						if err := server.Db.Save(&smsQuota).Error; err != nil {
							log.Println("Failed to update SMS quota:", err)
						}
						quotaMutex.Unlock()
						counter += 1
					}
					wg.Done()
				}(v)
			}
		}
		wg.Wait()
		if counter == 0 {
			return c.Status(400).JSON(map[string]interface{}{"error": "No SMS sent"})
		}
		return c.JSON(map[string]interface{}{"message": strconv.Itoa(counter) + " SMS(s) sent successfully"})
	}
}

// GET: /communication/birthday_wish
func HandleCommunicationGetBirthdayWish(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		var birthdayWishConfig DentalClinicBirthdayWishConfig
		err := server.Db.Where("clinic_id = ?", user.ClinicID).Take(&birthdayWishConfig).Error

		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]interface{}{"error": "Birthday wish config not found"})
			}
			log.Println(err)
			return c.Status(500).JSON(map[string]interface{}{"error": "Failed to fetch birthday wish config"})
		}

		return c.JSON(map[string]interface{}{
			"birthday_wish": birthdayWishConfig.BirthdayWish.String,
		})
	}
}

// POST: /communication/birthday_wish/toggle
func HandleCommunicationToggleBirthdayWish(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Enabled  *bool  `json:"enabled"`
		Language string `json:"language"` // "ar" or "en"
	}

	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)

		if err := json.Unmarshal(c.Body(), body); err != nil {
			return c.Status(400).JSON(map[string]interface{}{"error": "Invalid request body"})
		}

		var birthdayWishStatus DentalClinicBirthdayWish

		if body.Enabled == nil || !*body.Enabled {
			birthdayWishStatus = ""
		} else {
			switch body.Language {
			case "ar":
				birthdayWishStatus = DentailClinicBirthdayWishEnabledAR
			case "en":
				birthdayWishStatus = DentailClinicBirthdayWishEnabledEn
			default:
				return c.Status(400).JSON(map[string]interface{}{"error": "Invalid language."})
			}
		}

		var birthdayWishConfig DentalClinicBirthdayWishConfig
		err := server.Db.Where("clinic_id = ?", user.ClinicID).Take(&birthdayWishConfig).Error

		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				birthdayWishConfig = DentalClinicBirthdayWishConfig{
					ClinicID: user.ClinicID,
				}
				if birthdayWishStatus == "" {
					birthdayWishConfig.BirthdayWish = sql.NullString{
						String: "",
						Valid:  false,
					}
				} else {
					birthdayWishConfig.BirthdayWish = sql.NullString{
						String: string(birthdayWishStatus),
						Valid:  true,
					}
				}
				if err := server.Db.Create(&birthdayWishConfig).Error; err != nil {
					log.Println(err)
					return c.Status(500).JSON(map[string]interface{}{"error": "Failed to create birthday wish config"})
				}
			} else {
				return c.Status(500).JSON(map[string]interface{}{"error": "Failed to fetch birthday wish config"})
			}
		} else {
			if birthdayWishStatus == "" {
				birthdayWishConfig.BirthdayWish = sql.NullString{
					String: "",
					Valid:  false,
				}
			} else {
				birthdayWishConfig.BirthdayWish = sql.NullString{
					String: string(birthdayWishStatus),
					Valid:  true,
				}
			}
			if err := server.Db.Save(&birthdayWishConfig).Error; err != nil {
				log.Println(err)
				return c.Status(500).JSON(map[string]interface{}{"error": "Failed to update birthday wish config"})
			}
		}

		return c.JSON(map[string]interface{}{
			"message":       "Birthday wish setting updated successfully",
			"birthday_wish": string(birthdayWishStatus),
		})
	}
}
