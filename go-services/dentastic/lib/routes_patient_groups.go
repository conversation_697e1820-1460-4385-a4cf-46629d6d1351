package lib

import (
	"encoding/json"
	"errors"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gorm.io/gorm"
)

type JsonPatientGroup struct {
	DentalClinicPatientGroup
	Clinic       string `json:"clinic,omitempty"`
	MembersCount int    `json:"membersCount,omitempty"`
}

type JsonPatientGroupMember struct {
	DentalClinicPatientGroupMember
	Clinic  string `json:"clinic,omitempty"`
	Group   string `json:"group,omitempty"`
	Patient string `json:"patient,omitempty"`
}

func HandlePatientGroupsList(server *config.Server) func(*fiber.Ctx) error {
	type PatientGroupMembersCount struct {
		GroupID string `json:"groupId"`
		Count   int    `json:"count"`
	}
	return func(c *fiber.Ctx) error {
		search := c.Query("search", "")
		user := c.UserContext().Value("user").(DentalClinicUser)
		var groups []DentalClinicPatientGroup
		q := server.Db.Where("clinic_id = ? AND deleted_at IS NULL", user.ClinicID)
		if len(search) != 0 {
			q = q.Where("name ILIKE ?", "%"+search+"%")
		}
		if err := q.Find(&groups).Error; err != nil {
			panic(err)
		}
		groupIds := make([]string, len(groups))
		for i, group := range groups {
			groupIds[i] = group.ID
		}
		var aggregate []PatientGroupMembersCount
		aq := server.Db.Model(&DentalClinicPatientGroupMember{}).Select("group_id, COUNT(*) as count").Group("group_id").Having("group_id IN ?", groupIds)
		if err := aq.Scan(&aggregate).Error; err != nil {
			panic(err)
		}
		var jsonGroups []JsonPatientGroup
		for _, group := range groups {
			var membersCount int
			for _, a := range aggregate {
				if a.GroupID == group.ID {
					membersCount = a.Count
					break
				}
			}
			jsonGroup := JsonPatientGroup{DentalClinicPatientGroup: group, MembersCount: membersCount}
			jsonGroups = append(jsonGroups, jsonGroup)
		}
		return c.JSON(map[string]interface{}{"patientGroups": jsonGroups})
	}
}

func HandlePatientGroupsCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name string `json:"name"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		group := DentalClinicPatientGroup{}
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		group.ClinicID = user.ClinicID
		group.Name = body.Name

		var conflictGroup DentalClinicPatientGroup
		// check for existing group with the same name
		if err := server.Db.Where("clinic_id = ? AND name ILIKE ?", user.ClinicID, body.Name).First(&conflictGroup).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				panic(err)
			}
		}
		if conflictGroup.ID != "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"message": "Group with the same name already exists"})
		}
		if err := server.Db.Create(&group).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"patientGroup": group})
	}
}

func HandlePatientGroupsUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Name string `json:"name"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		group := DentalClinicPatientGroup{}
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		if err := server.Db.Where("clinic_id = ? AND id = ?", user.ClinicID, c.Params("id")).First(&group).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"message": "Group not found"})
			}
			panic(err)
		}
		group.Name = body.Name
		// check for existing group with the same name
		if err := server.Db.Where("clinic_id = ? AND name ILIKE ?", user.ClinicID, body.Name).First(&group).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				panic(err)
			}
		}
		if group.ID != "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"message": "Group with the same name already exists"})
		}
		if err := server.Db.Save(&group).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"patientGroup": group})
	}
}

func HandlePatientGroupsDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		groupId := c.Params("id")
		user := c.UserContext().Value("user").(DentalClinicUser)
		group := DentalClinicPatientGroup{}
		if err := server.Db.Where("clinic_id = ? AND id = ?", user.ClinicID, groupId).First(&group).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"message": "Group not found"})
			}
			panic(err)
		}
		if err := server.Db.Delete(&group).Error; err != nil {
			panic(err)
		}
		return c.JSON(fiber.Map{"message": "Group deleted"})
	}
}

func HandlePatientGroupsMembersList(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		var members []DentalClinicPatientGroupMember
		if err := server.Db.Where("clinic_id = ? AND group_id = ?", user.ClinicID, c.Params("id")).Find(&members).Error; err != nil {
			panic(err)
		}
		var jsonMembers []JsonPatientGroupMember
		for _, member := range members {
			jsonMember := JsonPatientGroupMember{DentalClinicPatientGroupMember: member}
			jsonMembers = append(jsonMembers, jsonMember)
		}
		return c.JSON(map[string]interface{}{"patientGroupMembers": jsonMembers})
	}
}

func HandlePatientGroupsMembersCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		PatientID string `json:"patientId"`
	}
	return func(c *fiber.Ctx) error {
		id := c.Params("id")
		user := c.UserContext().Value("user").(DentalClinicUser)
		member := DentalClinicPatientGroupMember{}
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		// check if patient already member of group
		conflictMember := DentalClinicPatientGroupMember{}
		if err := server.Db.Where("clinic_id = ? AND group_id = ? AND patient_id = ?", user.ClinicID, id, body.PatientID).First(&conflictMember).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				panic(err)
			}
		}
		if conflictMember.ID != "" {
			return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{"message": "Patient already member of group"})
		}
		member.ClinicID = user.ClinicID
		member.GroupID = id
		member.PatientID = body.PatientID
		if err := server.Db.Create(&member).Error; err != nil {
			panic(err)
		}
		return c.JSON(map[string]interface{}{"patientGroupMember": member})
	}
}

func HandlePatientGroupsMembersDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		id := c.Params("id")
		memberId := c.Params("memberId")
		user := c.UserContext().Value("user").(DentalClinicUser)
		member := DentalClinicPatientGroupMember{}
		if err := server.Db.Where("clinic_id = ? AND group_id = ? AND patient_id = ?", user.ClinicID, id, memberId).First(&member).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(fiber.StatusNotFound).JSON(fiber.Map{"message": "Member not found in group"})
			}
			panic(err)
		}
		// NOTE: hard delete
		if err := server.Db.Delete(&member).Error; err != nil {
			panic(err)
		}
		return c.JSON(fiber.Map{"message": "Member deleted"})
	}
}
