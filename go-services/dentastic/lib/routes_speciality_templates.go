package lib

import (
	"encoding/json"
	"errors"

	"github.com/gofiber/fiber/v2"
	"gitlab.com/payrows/core/config"
	"gitlab.com/payrows/core/models"
	"gitlab.com/payrows/core/responses"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type JsonSpecialityTemplate struct {
	DentalClinicSpecialityTemplate
	Clinic    string `json:"clinic,omitemtpy"`
	CreatedBy string `json:"createdBy,omitemtpy"`
}

// GET: /speciality-templates
func HandleSpecialityTemplatesGet(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		var specialities []DentalClinicSpecialityTemplate
		if err := server.Db.Where("clinic_id = ?", user.ClinicID).Find(&specialities).Error; err != nil {
			panic(err)
		}

		jsonSpecialities := make([]JsonSpecialityTemplate, len(specialities))

		for index, v := range specialities {
			jsonSpecialities[index] = JsonSpecialityTemplate{DentalClinicSpecialityTemplate: v}
		}

		return c.JSON(map[string]interface{}{"specialities": jsonSpecialities})
	}
}

// POST: /speciality-templates
func HandleSpecialityTemplateCreate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Speciality string `json:"speciality"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}
		speciality := new(DentalClinicSpecialityTemplate)
		if len(body.Speciality) == 0 {
			return responses.Send400(c, "Invalid speciality name")
		}
		speciality.ClinicID = user.ClinicID
		speciality.CreatedByID = user.ID
		speciality.UpdatedByID = user.ID
		speciality.Speciality = body.Speciality

		if err = server.Db.Create(speciality).Error; err != nil {
			panic(err)
		}

		jsonSpeciality := JsonSpecialityTemplate{DentalClinicSpecialityTemplate: *speciality}

		return c.Status(201).JSON(map[string]interface{}{"speciality": jsonSpeciality})
	}
}

// POST: /speciality-templates/bulk
func HandleSpecialityTemplateBulkCreate(server *config.Server) func(*fiber.Ctx) error {
	type ProcedureEntry struct {
		Procedure    string  `json:"procedure"`
		Price        float64 `json:"price"`
		ToothRemoved bool    `json:"toothRemoved"`
		Crown        bool    `json:"crown"`
		Endo         bool    `json:"endo"`
		Implant      bool    `json:"implant"`
		Operative    string  `json:"operative"`
	}
	type SpeciailityEntry struct {
		Speciality string           `json:"speciality"`
		Procedures []ProcedureEntry `json:"procedures"`
	}
	type request struct {
		Specialities []SpeciailityEntry `json:"specialities"`
	}
	return func(c *fiber.Ctx) error {
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		if len(body.Specialities) == 0 {
			return responses.Send400(c, "No specialities provided")
		}

		specialities := make([]DentalClinicSpecialityTemplate, len(body.Specialities))
		procedures := make([]DentalClinicProcedureTemplate, 0)
		for index, v := range body.Specialities {
			specialities[index] = DentalClinicSpecialityTemplate{
				ClinicID:    user.ClinicID,
				Speciality:  v.Speciality,
				CreatedByID: user.ID,
				UpdatedByID: user.ID,
			}
			specialities[index].ID = models.MakeID()
			for _, p := range v.Procedures {
				procedures = append(procedures, DentalClinicProcedureTemplate{
					ClinicID:     user.ClinicID,
					CreatedByID:  user.ID,
					UpdatedByID:  user.ID,
					SpecialityID: specialities[index].ID,
					Procedure:    p.Procedure,
					Price:        p.Price,
					ToothRemoved: p.ToothRemoved,
					Crown:        p.Crown,
					Endo:         p.Endo,
					Implant:      p.Implant,
					Operative:    p.Operative,
				})
			}
		}

		err = server.Db.Transaction(func(tx *gorm.DB) error {
			if err := server.Db.Create(&specialities).Error; err != nil {
				return err
			}
			if err := server.Db.Create(&procedures).Error; err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			panic(err)
		}

		jsonSpecialities := make([]JsonSpecialityTemplate, len(specialities))
		jsonProcedures := make([]JsonProcedureTemplate, 0)
		for index, v := range specialities {
			jsonSpecialities[index] = JsonSpecialityTemplate{DentalClinicSpecialityTemplate: v}
		}
		for index := range procedures {
			jsonProcedures = append(jsonProcedures, JsonProcedureTemplate{DentalClinicProcedureTemplate: procedures[index]})
		}

		return c.Status(201).JSON(map[string]interface{}{"specialities": jsonSpecialities, "procedures": jsonProcedures})
	}
}

// GET: /speciality-templates/:sid
func HandleSpecialityTemplateGetOne(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		specialityId := c.Params("sid")
		user := c.UserContext().Value("user").(DentalClinicUser)

		speciality := new(DentalClinicSpecialityTemplate)
		if err := server.Db.Where("id = ? AND clinic_id = ?", specialityId, user.ClinicID).Take(speciality).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Speciality not found"})
			}
			panic(err)
		}

		var procedures []DentalClinicProcedureTemplate
		if err := server.Db.Where("speciality_id = ? AND clinic_id = ?", speciality.ID, user.ClinicID).Find(&procedures).Error; err != nil {
			panic(err)
		}

		jsonProcedures := make([]JsonProcedureTemplate, len(procedures))
		for index, v := range procedures {
			jsonProcedures[index] = JsonProcedureTemplate{DentalClinicProcedureTemplate: v}
		}

		jsonSpeciality := JsonSpecialityTemplate{DentalClinicSpecialityTemplate: *speciality}

		return c.JSON(map[string]interface{}{"procedures": jsonProcedures, "speciality": jsonSpeciality})
	}
}

// PATCH: /speciality-templates/:sid
func HandleSpecialityTemplateUpdate(server *config.Server) func(*fiber.Ctx) error {
	type request struct {
		Speciality string `json:"speciality"`
	}
	return func(c *fiber.Ctx) error {
		specialityId := c.Params("sid")
		user := c.UserContext().Value("user").(DentalClinicUser)
		body := new(request)
		err := json.Unmarshal(c.Body(), body)
		if err != nil {
			panic(err)
		}

		speciality := new(DentalClinicSpecialityTemplate)
		if err = server.Db.Where("id = ? AND clinic_id = ?", specialityId, user.ClinicID).Take(speciality).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Speciality not found"})
			}
			panic(err)
		}

		updateObj := map[string]interface{}{}

		if len(body.Speciality) > 0 {
			updateObj["speciality"] = body.Speciality
		}

		if len(updateObj) == 0 {
			return responses.Send400(c, "Nothing to update")
		}

		if err = server.Db.Model(speciality).Clauses(clause.Returning{}).UpdateColumns(updateObj).Error; err != nil {
			panic(err)
		}

		jsonSpeciality := JsonSpecialityTemplate{DentalClinicSpecialityTemplate: *speciality}

		return c.JSON(map[string]interface{}{"speciality": jsonSpeciality})
	}
}

// DELETE: /speciality-templates/:sid
func HandleSpecialityTemplateDelete(server *config.Server) func(*fiber.Ctx) error {
	return func(c *fiber.Ctx) error {
		specialityId := c.Params("sid")
		user := c.UserContext().Value("user").(DentalClinicUser)

		speciality := new(DentalClinicSpecialityTemplate)
		if err := server.Db.Where("id = ? AND clinic_id = ?", specialityId, user.ClinicID).Take(speciality).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.Status(404).JSON(map[string]string{"error": "Speciality not found"})
			}
			panic(err)
		}

		var linkedProcedures []DentalClinicProcedureTemplate
		if err := server.Db.Where("speciality_id = ? AND clinic_id = ?", specialityId, user.ClinicID).Find(&linkedProcedures).Error; err != nil {
			panic(err)
		}

		err := server.Db.Transaction(func(tx *gorm.DB) error {
			for _, lp := range linkedProcedures {
				if err := tx.Delete(&lp).Error; err != nil {
					panic(err)
				}
			}
			if err := tx.Delete(speciality).Error; err != nil {
				panic(err)
			}
			return nil
		})
		if err != nil {
			panic(err)
		}

		return c.Status(204).JSON(map[string]string{"message": "Speciality and linked procedures deleted"})
	}
}
