# Dentastic Makefile
# Provides convenient commands for development, testing, and deployment

.PHONY: help test test-auth test-merchant test-database test-errors test-coverage test-race test-verbose clean build run migrate dev-setup

# Default target
help:
	@echo "Available commands:"
	@echo "  make test           - Run all integration tests"
	@echo "  make test-auth      - Run authentication tests only"
	@echo "  make test-merchant  - Run merchant/clinic tests only"
	@echo "  make test-database  - Run database integration tests only"
	@echo "  make test-errors    - Run error handling tests only"
	@echo "  make test-coverage  - Run tests with coverage report"
	@echo "  make test-race      - Run tests with race detection"
	@echo "  make test-verbose   - Run tests with verbose output"
	@echo "  make clean          - Clean test artifacts and temporary files"
	@echo "  make build          - Build the application"
	@echo "  make run            - Run the application"
	@echo "  make migrate        - Run database migrations"
	@echo "  make dev-setup      - Set up development environment"

# Test commands
test:
	@echo "Running all integration tests..."
	@go test ./tests/... -timeout 30s

test-auth:
	@echo "Running authentication tests..."
	@go test ./tests/auth_test.go ./tests/main_test.go ./tests/test_utils.go -v -timeout 30s

test-merchant:
	@echo "Running merchant/clinic tests..."
	@go test ./tests/merchant_test.go ./tests/main_test.go ./tests/test_utils.go -v -timeout 30s

test-database:
	@echo "Running database integration tests..."
	@go test ./tests/database_test.go ./tests/main_test.go ./tests/test_utils.go -v -timeout 30s

test-errors:
	@echo "Running error handling tests..."
	@go test ./tests/error_handling_test.go ./tests/main_test.go ./tests/test_utils.go -v -timeout 30s

test-legacy:
	@echo "Running legacy tests..."
	@go test ./tests/login_test.go ./tests/main_test.go ./tests/test_utils.go -v -timeout 30s

test-coverage:
	@echo "Running tests with coverage..."
	@go test ./tests/... -coverprofile=coverage.out -timeout 30s
	@go tool cover -func=coverage.out
	@echo "Coverage report saved to coverage.out"
	@echo "Generate HTML report with: make coverage-html"

coverage-html: coverage.out
	@echo "Generating HTML coverage report..."
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report saved to coverage.html"

test-race:
	@echo "Running tests with race detection..."
	@go test ./tests/... -race -timeout 60s

test-verbose:
	@echo "Running tests with verbose output..."
	@go test ./tests/... -v -timeout 30s

test-short:
	@echo "Running short tests only..."
	@go test ./tests/... -short -timeout 15s

# Specific test functions
test-auth-flow:
	@echo "Running authentication flow test..."
	@go test ./tests/... -run TestAuthenticationFlow -v

test-role-access:
	@echo "Running role-based access tests..."
	@go test ./tests/... -run TestRoleBasedAccess -v

test-db-integration:
	@echo "Running database integration tests..."
	@go test ./tests/... -run TestDatabaseIntegration -v

# Development commands
build:
	@echo "Building application..."
	@go build -o bin/dentastic .

run:
	@echo "Running application..."
	@go run .

run-dev:
	@echo "Running application in development mode..."
	@go run . -migrate

migrate:
	@echo "Running database migrations..."
	@go run . -migrate -exit-after-migrate

migrate-merchant:
	@echo "Running merchant migrations..."
	@go run . -migrate-merchant -exit-after-migrate

dev-setup:
	@echo "Setting up development environment..."
	@go mod download
	@go mod tidy
	@echo "Installing development tools..."
	@go install github.com/cosmtrek/air@latest
	@echo "Development environment ready!"

# Cleanup commands
clean:
	@echo "Cleaning up..."
	@rm -f coverage.out coverage.html
	@rm -f bin/dentastic
	@rm -f /tmp/payrows_dentastic_test_*.db
	@echo "Cleanup complete!"

clean-test-db:
	@echo "Cleaning test databases..."
	@rm -f /tmp/payrows_dentastic_test_*.db
	@echo "Test databases cleaned!"

# Docker commands
docker-build:
	@echo "Building Docker image..."
	@docker build -t dentastic:latest .

docker-test:
	@echo "Running tests in Docker..."
	@docker run --rm dentastic:latest go test ./tests/... -v

# Linting and formatting
fmt:
	@echo "Formatting code..."
	@go fmt ./...

vet:
	@echo "Running go vet..."
	@go vet ./...

lint:
	@echo "Running golangci-lint..."
	@golangci-lint run

# Quality checks
quality: fmt vet test-coverage
	@echo "Quality checks complete!"

# CI/CD commands
ci-test:
	@echo "Running CI tests..."
	@go test ./tests/... -v -race -coverprofile=coverage.out -timeout 60s
	@go tool cover -func=coverage.out

ci-build:
	@echo "Building for CI..."
	@CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/dentastic .

# Database utilities
db-reset:
	@echo "Resetting development database..."
	@go run . -migrate -exit-after-migrate

db-seed:
	@echo "Seeding database with test data..."
	@go run cmd/mock/main.go

# Performance testing
test-bench:
	@echo "Running benchmark tests..."
	@go test ./tests/... -bench=. -benchmem

# Security testing
test-security:
	@echo "Running security tests..."
	@go test ./tests/... -run "TestSQLInjection|TestXSS|TestCSRF" -v

# Dependency management
deps-update:
	@echo "Updating dependencies..."
	@go get -u ./...
	@go mod tidy

deps-check:
	@echo "Checking for vulnerabilities..."
	@go list -json -deps ./... | nancy sleuth

# Documentation
docs:
	@echo "Generating documentation..."
	@godoc -http=:6060
	@echo "Documentation available at http://localhost:6060"

# Watch mode for development
watch:
	@echo "Starting development server with auto-reload..."
	@air

# Environment setup
env-example:
	@echo "Creating example environment file..."
	@cat > .env.example << 'EOF'
# Database Configuration
DB_URL=postgresql://postgres:password@localhost:5432/payrows_dentastic
# DB_URL=sqlite://dentastic.db

# Server Configuration
PORT=3000
JWT_SECRET=your_jwt_secret_here

# AWS S3 Configuration
S3_KEY=your_s3_key
S3_SECRET=your_s3_secret
S3_ENDPOINT=your_s3_endpoint
S3_REGION=your_s3_region
S3_BUCKET=your_s3_bucket

# Stripe Configuration
STRIPE_KEY=sk_test_your_stripe_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_key

# WriteSonic Configuration
WRITE_SONIC_API_KEY=your_writesonic_key
WRITE_SONIC_BOT_URL=your_writesonic_bot_url
EOF
	@echo "Example environment file created: .env.example"

# Help for specific test categories
help-tests:
	@echo "Test Categories:"
	@echo ""
	@echo "Authentication Tests (test-auth):"
	@echo "  - Login/logout functionality"
	@echo "  - Registration validation"
	@echo "  - JWT token handling"
	@echo "  - Password reset flow"
	@echo ""
	@echo "Merchant Tests (test-merchant):"
	@echo "  - Clinic management"
	@echo "  - User management"
	@echo "  - Role-based access control"
	@echo "  - Subscription validation"
	@echo ""
	@echo "Database Tests (test-database):"
	@echo "  - CRUD operations"
	@echo "  - Relationship loading"
	@echo "  - Transaction handling"
	@echo "  - Constraint validation"
	@echo ""
	@echo "Error Handling Tests (test-errors):"
	@echo "  - Input validation"
	@echo "  - Error responses"
	@echo "  - Edge cases"
	@echo "  - Security testing"

# Default target when no arguments provided
.DEFAULT_GOAL := help
